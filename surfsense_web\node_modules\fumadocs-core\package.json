{"name": "fumadocs-core", "version": "15.5.2", "description": "The library for building a documentation website in Next.js", "keywords": ["NextJs", "Docs"], "homepage": "https://fumadocs.vercel.app", "repository": "github:fuma-nama/fumadocs", "license": "MIT", "author": "<PERSON><PERSON>", "type": "module", "exports": {"./sidebar": {"import": "./dist/sidebar.js", "types": "./dist/sidebar.d.ts"}, "./breadcrumb": {"import": "./dist/breadcrumb.js", "types": "./dist/breadcrumb.d.ts"}, "./toc": {"import": "./dist/toc.js", "types": "./dist/toc.d.ts"}, "./content": {"import": "./dist/content/index.js", "types": "./dist/content/index.d.ts"}, "./hide-if-empty": {"import": "./dist/hide-if-empty.js", "types": "./dist/hide-if-empty.d.ts"}, "./search/*": {"import": "./dist/search/*.js", "types": "./dist/search/*.d.ts"}, "./server": {"import": "./dist/server/index.js", "types": "./dist/server/index.d.ts"}, "./source": {"import": "./dist/source/index.js", "types": "./dist/source/index.d.ts"}, "./utils/*": {"import": "./dist/utils/*.js", "types": "./dist/utils/*.d.ts"}, "./link": {"import": "./dist/link.js", "types": "./dist/link.d.ts"}, "./dynamic-link": {"import": "./dist/dynamic-link.js", "types": "./dist/dynamic-link.d.ts"}, "./mdx-plugins": {"import": "./dist/mdx-plugins/index.js", "types": "./dist/mdx-plugins/index.d.ts"}, "./i18n": {"import": "./dist/i18n/index.js", "types": "./dist/i18n/index.d.ts"}, "./highlight": {"import": "./dist/highlight/index.js", "types": "./dist/highlight/index.d.ts"}, "./highlight/client": {"import": "./dist/highlight/client.js", "types": "./dist/highlight/client.d.ts"}, "./framework": {"import": "./dist/framework/index.js", "types": "./dist/framework/index.d.ts"}, "./framework/*": {"import": "./dist/framework/*.js", "types": "./dist/framework/*.d.ts"}}, "files": ["dist/*"], "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "@orama/orama": "^3.1.6", "@shikijs/rehype": "^3.6.0", "@shikijs/transformers": "^3.6.0", "github-slugger": "^2.0.0", "hast-util-to-estree": "^3.1.3", "hast-util-to-jsx-runtime": "^2.3.6", "image-size": "^2.0.2", "negotiator": "^1.0.0", "react-remove-scroll": "^2.7.1", "remark": "^15.0.0", "remark-gfm": "^4.0.1", "remark-rehype": "^11.1.2", "scroll-into-view-if-needed": "^3.1.0", "shiki": "^3.6.0", "unist-util-visit": "^5.0.0"}, "devDependencies": {"@mdx-js/mdx": "^3.1.0", "@oramacloud/client": "^2.1.4", "@tanstack/react-router": "^1.121.2", "@types/estree-jsx": "^1.0.5", "@types/hast": "^3.0.4", "@types/mdast": "^4.0.3", "@types/negotiator": "^0.6.4", "@types/node": "24.0.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "algoliasearch": "5.27.0", "mdast-util-mdx-jsx": "^3.2.0", "mdast-util-mdxjs-esm": "^2.0.1", "next": "^15.3.3", "react-router": "^7.6.2", "remark-mdx": "^3.1.0", "typescript": "^5.8.3", "unified": "^11.0.5", "vfile": "^6.0.3", "eslint-config-custom": "0.0.0", "tsconfig": "0.0.0"}, "peerDependencies": {"@oramacloud/client": "1.x.x || 2.x.x", "algoliasearch": "5.x.x", "next": "14.x.x || 15.x.x", "react": "18.x.x || 19.x.x", "react-dom": "18.x.x || 19.x.x", "@types/react": "*"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@oramacloud/client": {"optional": true}, "algoliasearch": {"optional": true}, "next": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}}, "publishConfig": {"access": "public"}, "scripts": {"build": "tsup", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsup --watch", "lint": "eslint .", "types:check": "tsc --noEmit"}}
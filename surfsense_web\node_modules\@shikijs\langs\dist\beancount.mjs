const lang = Object.freeze(JSON.parse("{\"displayName\":\"Beancount\",\"fileTypes\":[\"beancount\"],\"name\":\"beancount\",\"patterns\":[{\"match\":\";.*\",\"name\":\"comment.line.beancount\"},{\"begin\":\"^\\\\s*(p(?:op|ush)tag)\\\\s+(#)([\\\\--9A-Z_a-z]+)\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.beancount\"},\"2\":{\"name\":\"keyword.operator.tag.beancount\"},\"3\":{\"name\":\"entity.name.tag.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.tag.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#illegal\"}]},{\"begin\":\"^\\\\s*(include)\\\\s+(\\\".*\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.beancount\"},\"2\":{\"name\":\"string.quoted.double.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.include.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#illegal\"}]},{\"begin\":\"^\\\\s*(option)\\\\s+(\\\".*\\\")\\\\s+(\\\".*\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.beancount\"},\"2\":{\"name\":\"support.variable.beancount\"},\"3\":{\"name\":\"string.quoted.double.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.option.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#illegal\"}]},{\"begin\":\"^\\\\s*(plugin)\\\\s*(\\\"(.*?)\\\")\\\\s*(\\\".*?\\\")?\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.beancount\"},\"2\":{\"name\":\"string.quoted.double.beancount\"},\"3\":{\"name\":\"entity.name.function.beancount\"},\"4\":{\"name\":\"string.quoted.double.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"keyword.operator.directive.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s+(open|close|pad)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#account\"},{\"include\":\"#commodity\"},{\"match\":\",\",\"name\":\"punctuation.separator.beancount\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s+(custom)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#string\"},{\"include\":\"#bool\"},{\"include\":\"#amount\"},{\"include\":\"#number\"},{\"include\":\"#date\"},{\"include\":\"#account\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s(event)\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.directive.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#string\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s(commodity)\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.directive.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#commodity\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s(note|document)\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.directive.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#account\"},{\"include\":\"#string\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s(price)\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.directive.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#commodity\"},{\"include\":\"#amount\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s(balance)\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.directive.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.dated.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#meta\"},{\"include\":\"#account\"},{\"include\":\"#amount\"},{\"include\":\"#illegal\"}]},{\"begin\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\\\\s*(txn|[!#%\\\\&*?CMPR-U])\\\\s*(\\\".*?\\\")?\\\\s*(\\\".*?\\\")?\",\"beginCaptures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"},\"6\":{\"name\":\"support.function.directive.beancount\"},\"7\":{\"name\":\"string.quoted.tiers.beancount\"},\"8\":{\"name\":\"string.quoted.narration.beancount\"}},\"end\":\"(?=^(\\\\s*$|\\\\S))\",\"name\":\"meta.directive.transaction.beancount\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#posting\"},{\"include\":\"#meta\"},{\"include\":\"#tag\"},{\"include\":\"#link\"},{\"include\":\"#illegal\"}]}],\"repository\":{\"account\":{\"begin\":\"([A-Z][a-z]+)(:)\",\"beginCaptures\":{\"1\":{\"name\":\"variable.language.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"}},\"end\":\"\\\\s\",\"name\":\"meta.account.beancount\",\"patterns\":[{\"begin\":\"(\\\\S+)(:?)\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.account.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"}},\"end\":\"(:?)|(\\\\s)\",\"patterns\":[{\"include\":\"$self\"},{\"include\":\"#illegal\"}]}]},\"amount\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.modifier.beancount\"},\"2\":{\"name\":\"constant.numeric.currency.beancount\"},\"3\":{\"name\":\"entity.name.type.commodity.beancount\"}},\"match\":\"([-+|]?)(\\\\d+(?:,\\\\d{3})*(?:\\\\.\\\\d*)?)\\\\s*([A-Z][-'.0-9A-Z_]{0,22}[0-9A-Z])\",\"name\":\"meta.amount.beancount\"},\"bool\":{\"captures\":{\"0\":{\"name\":\"constant.language.bool.beancount\"},\"2\":{\"name\":\"constant.numeric.currency.beancount\"},\"3\":{\"name\":\"entity.name.type.commodity.beancount\"}},\"match\":\"TRUE|FALSE\"},\"comments\":{\"captures\":{\"1\":{\"name\":\"comment.line.beancount\"}},\"match\":\"(;.*)$\"},\"commodity\":{\"match\":\"([A-Z][-'.0-9A-Z_]{0,22}[0-9A-Z])\",\"name\":\"entity.name.type.commodity.beancount\"},\"cost\":{\"begin\":\"\\\\{\\\\{?\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.assignment.beancount\"}},\"end\":\"}}?\",\"endCaptures\":{\"0\":{\"name\":\"keyword.operator.assignment.beancount\"}},\"name\":\"meta.cost.beancount\",\"patterns\":[{\"include\":\"#amount\"},{\"include\":\"#date\"},{\"match\":\",\",\"name\":\"punctuation.separator.beancount\"},{\"include\":\"#illegal\"}]},\"date\":{\"captures\":{\"1\":{\"name\":\"constant.numeric.date.year.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"},\"3\":{\"name\":\"constant.numeric.date.month.beancount\"},\"4\":{\"name\":\"punctuation.separator.beancount\"},\"5\":{\"name\":\"constant.numeric.date.day.beancount\"}},\"match\":\"([0-9]{4})([-/|])([0-9]{2})([-/|])([0-9]{2})\",\"name\":\"meta.date.beancount\"},\"flag\":{\"match\":\"(?<=\\\\s)([!#%\\\\&*?CMPR-U])(?=\\\\s+)\",\"name\":\"keyword.other.beancount\"},\"illegal\":{\"match\":\"\\\\S\",\"name\":\"invalid.illegal.unrecognized.beancount\"},\"link\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.link.beancount\"},\"2\":{\"name\":\"markup.underline.link.beancount\"}},\"match\":\"(\\\\^)([\\\\--9A-Z_a-z]+)\"},\"meta\":{\"begin\":\"^\\\\s*([a-z][-0-9A-Z_a-z]+)(:)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.directive.beancount\"},\"2\":{\"name\":\"punctuation.separator.beancount\"}},\"end\":\"\\\\n\",\"name\":\"meta.meta.beancount\",\"patterns\":[{\"include\":\"#string\"},{\"include\":\"#account\"},{\"include\":\"#bool\"},{\"include\":\"#commodity\"},{\"include\":\"#date\"},{\"include\":\"#tag\"},{\"include\":\"#amount\"},{\"include\":\"#number\"},{\"include\":\"#comments\"},{\"include\":\"#illegal\"}]},\"number\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.modifier.beancount\"},\"2\":{\"name\":\"constant.numeric.currency.beancount\"}},\"match\":\"([-+|]?)(\\\\d+(?:,\\\\d{3})*(?:\\\\.\\\\d*)?)\"},\"posting\":{\"begin\":\"^\\\\s+(?=([!A-Z]))\",\"end\":\"(?=^(\\\\s*$|\\\\S|\\\\s*[A-Z]))\",\"name\":\"meta.posting.beancount\",\"patterns\":[{\"include\":\"#meta\"},{\"include\":\"#comments\"},{\"include\":\"#flag\"},{\"include\":\"#account\"},{\"include\":\"#amount\"},{\"include\":\"#cost\"},{\"include\":\"#date\"},{\"include\":\"#price\"},{\"include\":\"#illegal\"}]},\"price\":{\"begin\":\"@@?\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.assignment.beancount\"}},\"end\":\"(?=([\\\\n;]))\",\"name\":\"meta.price.beancount\",\"patterns\":[{\"include\":\"#amount\"},{\"include\":\"#illegal\"}]},\"string\":{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.beancount\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.beancount\"}]},\"tag\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.tag.beancount\"},\"2\":{\"name\":\"entity.name.tag.beancount\"}},\"match\":\"(#)([\\\\--9A-Z_a-z]+)\"}},\"scopeName\":\"text.beancount\"}"))

export default [
lang
]

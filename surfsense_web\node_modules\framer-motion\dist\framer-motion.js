!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function n(t){var e=Object.create(null);return t&&Object.keys(t).forEach((function(n){if("default"!==n){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}})),e.default=t,Object.freeze(e)}var i=n(e),s=React,o=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function c(t,e,n){var i,s={},r=null,c=null;for(i in void 0!==n&&(r=""+n),void 0!==e.key&&(r=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,i)&&!u.hasOwnProperty(i)&&(s[i]=e[i]);if(t&&t.defaultProps)for(i in e=t.defaultProps)void 0===s[i]&&(s[i]=e[i]);return{$$typeof:o,type:t,key:r,ref:c,props:s,_owner:l.current}}const h=r,d=c,p=c,m=e.createContext({});function f(t){const n=e.useRef(null);return null===n.current&&(n.current=t()),n.current}const g="undefined"!=typeof window,y=g?e.useLayoutEffect:e.useEffect,v=e.createContext(null),x=e.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class w extends i.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,n=t instanceof HTMLElement&&t.offsetWidth||0,i=this.props.sizeRef.current;i.height=e.offsetHeight||0,i.width=e.offsetWidth||0,i.top=e.offsetTop,i.left=e.offsetLeft,i.right=n-i.width-i.left}return null}componentDidUpdate(){}render(){return this.props.children}}function P({children:t,isPresent:n,anchorX:s}){const o=e.useId(),r=e.useRef(null),a=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(x);return e.useInsertionEffect(()=>{const{width:t,height:e,top:i,left:u,right:c}=a.current;if(n||!r.current||!t||!e)return;const h="left"===s?"left: "+u:"right: "+c;r.current.dataset.motionPopId=o;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${o}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${h}px !important;\n            top: ${i}px !important;\n          }\n        `),()=>{document.head.removeChild(d)}},[n]),d(w,{isPresent:n,childRef:r,sizeRef:a,children:i.cloneElement(t,{ref:r})})}const S=({children:t,initial:n,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l,anchorX:u})=>{const c=f(T),h=e.useId(),p=e.useCallback(t=>{c.set(t,!0);for(const t of c.values())if(!t)return;o&&o()},[c,o]),m=e.useMemo(()=>({id:h,initial:n,isPresent:s,custom:r,onExitComplete:p,register:t=>(c.set(t,!1),()=>c.delete(t))}),a?[Math.random(),p]:[s,p]);return e.useMemo(()=>{c.forEach((t,e)=>c.set(e,!1))},[s]),i.useEffect(()=>{!s&&!c.size&&o&&o()},[s]),"popLayout"===l&&(t=d(P,{isPresent:s,anchorX:u,children:t})),d(v.Provider,{value:m,children:t})};function T(){return new Map}function b(t=!0){const n=e.useContext(v);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=e.useId();e.useEffect(()=>{if(t)return o(r)},[t]);const a=e.useCallback(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,a]:[!0]}const A=t=>t.key||"";function E(t){const n=[];return e.Children.forEach(t,t=>{e.isValidElement(t)&&n.push(t)}),n}const M=e.createContext(null);function C(t,e){-1===t.indexOf(e)&&t.push(e)}function V(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const R=t=>t;let D=R,k=R;const L={skipAnimations:!1,useManualTiming:!1};function B(t){let e;return()=>(void 0===e&&(e=t()),e)}const F=(t,e,n)=>{const i=e-t;return 0===i?1:(n-t)/i};class O{constructor(){this.subscriptions=[]}add(t){return C(this.subscriptions,t),()=>V(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const j=t=>1e3*t,I=t=>t/1e3;function U(t,e){return e?t*(1e3/e):0}const W=B(()=>void 0!==window.ScrollTimeline);class N extends class{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t,e){const n=this.animations.map(n=>W()&&n.attachTimeline?n.attachTimeline(t):"function"==typeof e?e(n):void 0);return()=>{n.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function z(t,e){return t?t[e]||t.default||t:void 0}function X(t){let e=0;let n=t.next(e);for(;!n.done&&e<2e4;)e+=50,n=t.next(e);return e>=2e4?1/0:e}function $(t,e=100,n){const i=n({...t,keyframes:[0,e]}),s=Math.min(X(i),2e4);return{type:"keyframes",ease:t=>i.next(s*t).value/e,duration:I(s)}}function H(t){return"function"==typeof t}function Y(t,e){t.timeline=e,t.onfinish=null}const K=t=>Array.isArray(t)&&"number"==typeof t[0],G={linearEasing:void 0};function _(t,e){const n=B(t);return()=>{var t;return null!==(t=G[e])&&void 0!==t?t:n()}}const q=_(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),Z=(t,e,n=10)=>{let i="";const s=Math.max(Math.round(e/n),2);for(let e=0;e<s;e++)i+=t(F(0,s-1,e))+", ";return`linear(${i.substring(0,i.length-2)})`};function J(t){return Boolean("function"==typeof t&&q()||!t||"string"==typeof t&&(t in tt||q())||K(t)||Array.isArray(t)&&t.every(J))}const Q=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,tt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Q([0,.65,.55,1]),circOut:Q([.55,0,1,.45]),backIn:Q([.31,.01,.66,-.59]),backOut:Q([.33,1.53,.69,.99])};const et=["read","resolveKeyframes","update","preRender","render","postRender"],nt={value:null,addProjectionMetrics:null};function it(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=et.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,i=new Set,s=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){r.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,o=!1)=>{const a=o&&s?n:i;return e&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{a=t,s?o=!0:(s=!0,[n,i]=[i,n],n.forEach(u),e&&nt.value&&nt.value.frameloop[e].push(l),l=0,n.clear(),s=!1,o&&(o=!1,c.process(t)))}};return c}(o,e?n:void 0),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=r,p=()=>{const o=L.useManualTiming?s.timestamp:performance.now();n=!1,L.useManualTiming||(s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1)),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(p))};return{schedule:et.reduce((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(p)),a.schedule(e,o,r)),e},{}),cancel:t=>{for(let e=0;e<et.length;e++)r[et[e]].cancel(t)},state:s,steps:r}}const{schedule:st,cancel:ot,state:rt,steps:at}=it("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:R,!0),{schedule:lt,cancel:ut}=it(queueMicrotask,!1);let ct;function ht(){ct=void 0}const dt={now:()=>(void 0===ct&&dt.set(rt.isProcessing||L.useManualTiming?rt.timestamp:performance.now()),ct),set:t=>{ct=t,queueMicrotask(ht)}},pt={x:!1,y:!1};function mt(){return pt.x||pt.y}function ft(t,e,n){var i;if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const o=null!==(i=null==n?void 0:n[t])&&void 0!==i?i:s.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}function gt(t,e){const n=ft(t),i=new AbortController;return[n,{passive:!0,...e,signal:i.signal},()=>i.abort()]}function yt(t){return!("touch"===t.pointerType||mt())}function vt(t,e,n={}){const[i,s,o]=gt(t,n),r=t=>{if(!yt(t))return;const{target:n}=t,i=e(n,t);if("function"!=typeof i||!n)return;const o=t=>{yt(t)&&(i(t),n.removeEventListener("pointerleave",o))};n.addEventListener("pointerleave",o,s)};return i.forEach(t=>{t.addEventListener("pointerenter",r,s)}),o}function xt(t,e){const n=e+"PointerCapture";if(t.target instanceof Element&&n in t.target&&void 0!==t.pointerId)try{t.target[n](t.pointerId)}catch(t){}}const wt=(t,e)=>!!e&&(t===e||wt(t,e.parentElement)),Pt=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,St=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const Tt=new WeakSet;function bt(t){return e=>{"Enter"===e.key&&t(e)}}function At(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function Et(t){return Pt(t)&&!mt()}function Mt(t,e,n={}){const[i,s,o]=gt(t,n),r=t=>{const n=t.currentTarget;if(!n||!Et(t)||Tt.has(n))return;Tt.add(n),xt(t,"set");const i=e(n,t),o=(t,e)=>{n.removeEventListener("pointerup",r),n.removeEventListener("pointercancel",a),xt(t,"release"),Et(t)&&Tt.has(n)&&(Tt.delete(n),"function"==typeof i&&i(t,{success:e}))},r=t=>{const e=!!t.isTrusted&&(i=t,s=n instanceof Element?n.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight},i.clientX<s.left||i.clientX>s.right||i.clientY<s.top||i.clientY>s.bottom);var i,s;o(t,!e&&(!(n instanceof Element)||wt(n,t.target)))},a=t=>{o(t,!1)};n.addEventListener("pointerup",r,s),n.addEventListener("pointercancel",a,s),n.addEventListener("lostpointercapture",a,s)};return i.forEach(t=>{let e=!1;var i;(t=n.useGlobalTarget?window:t)instanceof HTMLElement&&(e=!0,i=t,St.has(i.tagName)||-1!==i.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0)),t.addEventListener("pointerdown",r,s),e&&t.addEventListener("focus",t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const i=bt(()=>{if(Tt.has(n))return;At(n,"down");const t=bt(()=>{At(n,"up")});n.addEventListener("keyup",t,e),n.addEventListener("blur",()=>At(n,"cancel"),e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)})(t,s),s)}),o}const Ct={current:void 0};class Vt{constructor(t,e={}){this.version="12.5.0",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=dt.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=dt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new O);const n=this.events[t].add(e);return"change"===t?()=>{n(),st.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Ct.current&&Ct.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=dt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return U(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Rt(t,e){return new Vt(t,e)}const Dt=(t,e,n)=>t+(e-t)*n;function kt(t){return t.max-t.min}function Lt(t,e,n,i=.5){t.origin=i,t.originPoint=Dt(e.min,e.max,t.origin),t.scale=kt(n)/kt(e),t.translate=Dt(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Bt(t,e,n,i){Lt(t.x,e.x,n.x,i?i.originX:void 0),Lt(t.y,e.y,n.y,i?i.originY:void 0)}function Ft(t,e,n){t.min=n.min+e.min,t.max=t.min+kt(e)}function Ot(t,e,n){t.min=e.min-n.min,t.max=t.min+kt(e)}function jt(t,e,n){Ot(t.x,e.x,n.x),Ot(t.y,e.y,n.y)}const It=t=>!t.isLayoutDirty&&t.willUpdate(!1);function Ut(){const t=new Set,e=new WeakMap,n=()=>t.forEach(It);return{add:i=>{t.add(i),e.set(i,i.addEventListener("willUpdate",n))},remove:i=>{t.delete(i);const s=e.get(i);s&&(s(),e.delete(i)),n()},dirty:n}}const Wt=t=>Boolean(t&&t.getVelocity),Nt={current:!1},zt=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function Xt(t,e,n,i){if(t===e&&n===i)return R;const s=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=zt(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,n);return t=>0===t||1===t?t:zt(s(t),e,i)}const $t=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ht=t=>e=>1-t(1-e),Yt=Xt(.33,1.53,.69,.99),Kt=Ht(Yt),Gt=$t(Kt),_t=t=>(t*=2)<1?.5*Kt(t):.5*(2-Math.pow(2,-10*(t-1))),qt=t=>1-Math.sin(Math.acos(t)),Zt=Ht(qt),Jt=$t(qt),Qt=t=>/^0[^.\s]+$/u.test(t);const te=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ee=new Set(te),ne=new Set(["width","height","top","left","right","bottom",...te]),ie=(t,e,n)=>n>e?e:n<t?t:n,se={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},oe={...se,transform:t=>ie(0,1,t)},re={...se,default:1},ae=t=>Math.round(1e5*t)/1e5,le=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ue=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ce=(t,e)=>n=>Boolean("string"==typeof n&&ue.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),he=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(le);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},de={...se,transform:t=>Math.round((t=>ie(0,255,t))(t))},pe={test:ce("rgb","red"),parse:he("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+de.transform(t)+", "+de.transform(e)+", "+de.transform(n)+", "+ae(oe.transform(i))+")"};const me={test:ce("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:pe.transform},fe=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ge=fe("deg"),ye=fe("%"),ve=fe("px"),xe=fe("vh"),we=fe("vw"),Pe={...ye,parse:t=>ye.parse(t)/100,transform:t=>ye.transform(100*t)},Se={test:ce("hsl","hue"),parse:he("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+ye.transform(ae(e))+", "+ye.transform(ae(n))+", "+ae(oe.transform(i))+")"},Te={test:t=>pe.test(t)||me.test(t)||Se.test(t),parse:t=>pe.test(t)?pe.parse(t):Se.test(t)?Se.parse(t):me.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?pe.transform(t):Se.transform(t)},be=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Ae=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Ee(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(Ae,t=>(Te.test(t)?(i.color.push(o),s.push("color"),n.push(Te.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push("number"),n.push(parseFloat(t))),++o,"${}")).split("${}");return{values:n,split:r,indexes:i,types:s}}function Me(t){return Ee(t).values}function Ce(t){const{split:e,types:n}=Ee(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+="number"===e?ae(t[o]):"color"===e?Te.transform(t[o]):t[o]}return s}}const Ve=t=>"number"==typeof t?0:t;const Re={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(le))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(be))||void 0===n?void 0:n.length)||0)>0},parse:Me,createTransformer:Ce,getAnimatableNone:function(t){const e=Me(t);return Ce(t)(e.map(Ve))}},De=new Set(["brightness","contrast","saturate","opacity"]);function ke(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(le)||[];if(!i)return t;const s=n.replace(i,"");let o=De.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Le=/\b([a-z-]*)\(.*?\)/gu,Be={...Re,getAnimatableNone:t=>{const e=t.match(Le);return e?e.map(ke).join(" "):t}},Fe={borderWidth:ve,borderTopWidth:ve,borderRightWidth:ve,borderBottomWidth:ve,borderLeftWidth:ve,borderRadius:ve,radius:ve,borderTopLeftRadius:ve,borderTopRightRadius:ve,borderBottomRightRadius:ve,borderBottomLeftRadius:ve,width:ve,maxWidth:ve,height:ve,maxHeight:ve,top:ve,right:ve,bottom:ve,left:ve,padding:ve,paddingTop:ve,paddingRight:ve,paddingBottom:ve,paddingLeft:ve,margin:ve,marginTop:ve,marginRight:ve,marginBottom:ve,marginLeft:ve,backgroundPositionX:ve,backgroundPositionY:ve},Oe={rotate:ge,rotateX:ge,rotateY:ge,rotateZ:ge,scale:re,scaleX:re,scaleY:re,scaleZ:re,skew:ge,skewX:ge,skewY:ge,distance:ve,translateX:ve,translateY:ve,translateZ:ve,x:ve,y:ve,z:ve,perspective:ve,transformPerspective:ve,opacity:oe,originX:Pe,originY:Pe,originZ:ve},je={...se,transform:Math.round},Ie={...Fe,...Oe,zIndex:je,size:ve,fillOpacity:oe,strokeOpacity:oe,numOctaves:je},Ue={...Ie,color:Te,backgroundColor:Te,outlineColor:Te,fill:Te,stroke:Te,borderColor:Te,borderTopColor:Te,borderRightColor:Te,borderBottomColor:Te,borderLeftColor:Te,filter:Be,WebkitFilter:Be},We=t=>Ue[t];function Ne(t,e){let n=We(t);return n!==Be&&(n=Re),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const ze=new Set(["auto","none","0"]);const Xe=t=>180*t/Math.PI,$e=t=>{const e=Xe(Math.atan2(t[1],t[0]));return Ye(e)},He={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:$e,rotateZ:$e,skewX:t=>Xe(Math.atan(t[1])),skewY:t=>Xe(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ye=t=>((t%=360)<0&&(t+=360),t),Ke=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Ge=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),_e={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ke,scaleY:Ge,scale:t=>(Ke(t)+Ge(t))/2,rotateX:t=>Ye(Xe(Math.atan2(t[6],t[5]))),rotateY:t=>Ye(Xe(Math.atan2(-t[2],t[0]))),rotateZ:$e,rotate:$e,skewX:t=>Xe(Math.atan(t[4])),skewY:t=>Xe(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function qe(t){return t.includes("scale")?1:0}function Ze(t,e){if(!t||"none"===t)return qe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let i,s;if(n)i=_e,s=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=He,s=e}if(!s)return qe(e);const o=i[e],r=s[1].split(",").map(Je);return"function"==typeof o?o(r):r[o]}function Je(t){return parseFloat(t.trim())}const Qe=t=>t===se||t===ve,tn=new Set(["x","y","z"]),en=te.filter(t=>!tn.has(t));const nn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Ze(e,"x"),y:(t,{transform:e})=>Ze(e,"y")};nn.translateX=nn.x,nn.translateY=nn.y;const sn=new Set;let on=!1,rn=!1;function an(){if(rn){const t=Array.from(sn).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),n=new Map;e.forEach(t=>{const e=function(t){const e=[];return en.forEach(n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}(t);e.length&&(n.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();const e=n.get(t);e&&e.forEach(([e,n])=>{var i;null===(i=t.getValue(e))||void 0===i||i.set(n)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}rn=!1,on=!1,sn.forEach(t=>t.complete()),sn.clear()}function ln(){sn.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(rn=!0)})}class un{constructor(t,e,n,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(sn.add(this),on||(on=!0,st.read(ln),st.resolveKeyframes(an))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),sn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,sn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const cn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),hn=t=>e=>"string"==typeof e&&e.startsWith(t),dn=hn("--"),pn=hn("var(--"),mn=t=>!!pn(t)&&fn.test(t.split("/*")[0].trim()),fn=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,gn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function yn(t,e,n=1){const[i,s]=function(t){const e=gn.exec(t);if(!e)return[,];const[,n,i,s]=e;return["--"+(null!=n?n:i),s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return cn(t)?parseFloat(t):t}return mn(s)?yn(s,e,n+1):s}const vn=t=>e=>e.test(t),xn=[se,ve,ye,ge,we,xe,{test:t=>"auto"===t,parse:t=>t}],wn=t=>xn.find(vn(t));class Pn extends un{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let i=t[n];if("string"==typeof i&&(i=i.trim(),mn(i))){const s=yn(i,e.current);void 0!==s&&(t[n]=s),n===t.length-1&&(this.finalKeyframe=i)}}if(this.resolveNoneKeyframes(),!ne.has(n)||2!==t.length)return;const[i,s]=t,o=wn(i),r=wn(s);if(o!==r)if(Qe(o)&&Qe(r))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)("number"==typeof(i=t[e])?0===i:null===i||"none"===i||"0"===i||Qt(i))&&n.push(e);var i;n.length&&function(t,e,n){let i=0,s=void 0;for(;i<t.length&&!s;){const e=t[i];"string"==typeof e&&!ze.has(e)&&Ee(e).values.length&&(s=t[i]),i++}if(s&&n)for(const i of e)t[i]=Ne(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=nn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=nn[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,n])=>{e.getValue(t).set(n)}),this.resolveNoneKeyframes()}}const Sn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Re.test(t)&&"0"!==t||t.startsWith("url(")));function Tn(t,e,n,i){const s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;const o=t[t.length-1],r=Sn(s,e),a=Sn(o,e);return!(!r||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||H(n))&&i)}const bn=t=>null!==t;function An(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(bn),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}class En{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...r}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=dt.now(),this.options={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,...r},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ln(),an()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=dt.now(),this.hasAttemptedResolve=!0;const{name:n,type:i,velocity:s,delay:o,onComplete:r,onUpdate:a,isGenerator:l}=this.options;if(!l&&!Tn(t,n,i,s)){if(Nt.current||!o)return a&&a(An(t,this.options,e)),r&&r(),void this.resolveFinishedPromise();this.options.duration=0}const u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}function Mn(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Cn(t,e){return n=>n>0?e:t}const Vn=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},Rn=[me,pe,Se];function Dn(t){const e=(n=t,Rn.find(t=>t.test(n)));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===Se&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Mn(a,i,t+1/3),o=Mn(a,i,t),r=Mn(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const kn=(t,e)=>{const n=Dn(t),i=Dn(e);if(!n||!i)return Cn(t,e);const s={...n};return t=>(s.red=Vn(n.red,i.red,t),s.green=Vn(n.green,i.green,t),s.blue=Vn(n.blue,i.blue,t),s.alpha=Dt(n.alpha,i.alpha,t),pe.transform(s))},Ln=(t,e)=>n=>e(t(n)),Bn=(...t)=>t.reduce(Ln),Fn=new Set(["none","hidden"]);function On(t,e){return n=>Dt(t,e,n)}function jn(t){return"number"==typeof t?On:"string"==typeof t?mn(t)?Cn:Te.test(t)?kn:Wn:Array.isArray(t)?In:"object"==typeof t?Te.test(t)?kn:Un:Cn}function In(t,e){const n=[...t],i=n.length,s=t.map((t,n)=>jn(t)(t,e[n]));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function Un(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=jn(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const Wn=(t,e)=>{const n=Re.createTransformer(e),i=Ee(t),s=Ee(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?Fn.has(t)&&!s.values.length||Fn.has(e)&&!i.values.length?function(t,e){return Fn.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):Bn(In(function(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=null!==(n=t.values[a])&&void 0!==n?n:0;i[o]=l,s[r]++}return i}(i,s),s.values),n):Cn(t,e)};function Nn(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Dt(t,e,n);return jn(t)(t,e)}function zn(t,e,n){const i=Math.max(e-5,0);return U(n-t(i),e-i)}const Xn=100,$n=10,Hn=1,Yn=0,Kn=800,Gn=.3,_n=.3,qn={granular:.01,default:2},Zn={granular:.005,default:.5},Jn=.01,Qn=10,ti=.05,ei=1;function ni({duration:t=Kn,bounce:e=Gn,velocity:n=Yn,mass:i=Hn}){let s,o,r=1-e;r=ie(ti,ei,r),t=ie(Jn,Qn,I(t)),r<1?(s=e=>{const i=e*r,s=i*t;return.001-(i-n)/ii(e,r)*Math.exp(-s)},o=e=>{const i=e*r*t,o=i*n+n,a=Math.pow(r,2)*Math.pow(e,2)*t,l=Math.exp(-i),u=ii(Math.pow(e,2),r);return(.001-s(e)>0?-1:1)*((o-a)*l)/u}):(s=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,o=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let i=n;for(let n=1;n<12;n++)i-=t(i)/e(i);return i}(s,o,5/t);if(t=j(t),isNaN(a))return{stiffness:Xn,damping:$n,duration:t};{const e=Math.pow(a,2)*i;return{stiffness:e,damping:2*r*Math.sqrt(i*e),duration:t}}}function ii(t,e){return t*Math.sqrt(1-e*e)}const si=["duration","bounce"],oi=["stiffness","damping","mass"];function ri(t,e){return e.some(e=>void 0!==t[e])}function ai(t=_n,e=Gn){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:s}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:h,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:Yn,stiffness:Xn,damping:$n,mass:Hn,isResolvedFromDuration:!1,...t};if(!ri(t,oi)&&ri(t,si))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*ie(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:Hn,stiffness:s,damping:o}}else{const n=ni(t);e={...e,...n,mass:Hn},e.isResolvedFromDuration=!0}return e}({...n,velocity:-I(n.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*c)),g=r-o,y=I(Math.sqrt(l/c)),v=Math.abs(g)<5;let x;if(i||(i=v?qn.granular:qn.default),s||(s=v?Zn.granular:Zn.default),f<1){const t=ii(y,f);x=e=>{const n=Math.exp(-f*y*e);return r-n*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===f)x=t=>r-Math.exp(-y*t)*(g+(m+y*g)*t);else{const t=y*Math.sqrt(f*f-1);x=e=>{const n=Math.exp(-f*y*e),i=Math.min(t*e,300);return r-n*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}const w={calculatedDuration:p&&h||null,next:t=>{const e=x(t);if(p)a.done=t>=h;else{let n=0;f<1&&(n=0===t?j(m):zn(x,t,e));const o=Math.abs(n)<=i,l=Math.abs(r-e)<=s;a.done=o&&l}return a.value=a.done?r:e,a},toString:()=>{const t=Math.min(X(w),2e4),e=Z(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function li({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const y=t=>-m*Math.exp(-t/i),v=t=>g+y(t),x=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let w,P;const S=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,P=ai({keyframes:[d.value,p(d.value)],velocity:zn(v,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return S(0),{calculatedDuration:null,next:t=>{let e=!1;return P||void 0!==w||(e=!0,x(t),S(t)),void 0!==w&&t>=w?P.next(t-w):(!e&&x(t),d)}}}const ui=Xt(.42,0,1,1),ci=Xt(0,0,.58,1),hi=Xt(.42,0,.58,1),di=t=>Array.isArray(t)&&"number"!=typeof t[0],pi={linear:R,easeIn:ui,easeInOut:hi,easeOut:ci,circIn:qt,circInOut:Jt,circOut:Zt,backIn:Kt,backInOut:Gt,backOut:Yt,anticipate:_t},mi=t=>{if(K(t)){k(4===t.length);const[e,n,i,s]=t;return Xt(e,n,i,s)}return"string"==typeof t?pi[t]:t};function fi(t,e,{clamp:n=!0,ease:i,mixer:s}={}){const o=t.length;if(k(o===e.length),1===o)return()=>e[0];if(2===o&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,n){const i=[],s=n||Nn,o=t.length-1;for(let n=0;n<o;n++){let o=s(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||R:e;o=Bn(t,o)}i.push(o)}return i}(e,i,s),l=a.length,u=n=>{if(r&&n<t[0])return e[0];let i=0;if(l>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=F(t[i],t[i+1],n);return a[i](s)};return n?e=>u(ie(t[0],t[o-1],e)):u}function gi(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=F(0,e,i);t.push(Dt(n,1,s))}}function yi(t){const e=[0];return gi(e,t.length-1),e}function vi({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=di(i)?i.map(mi):mi(i),o={done:!1,value:e[0]},r=fi(function(t,e){return t.map(t=>t*e)}(n&&n.length===e.length?n:yi(e),t),e,{ease:Array.isArray(s)?s:(a=e,l=s,a.map(()=>l||hi).splice(0,a.length-1))});var a,l;return{calculatedDuration:t,next:e=>(o.value=r(e),o.done=e>=t,o)}}const xi=t=>{const e=({timestamp:e})=>t(e);return{start:()=>st.update(e,!0),stop:()=>ot(e),now:()=>rt.isProcessing?rt.timestamp:dt.now()}},wi={decay:li,inertia:li,tween:vi,keyframes:vi,spring:ai},Pi=t=>t/100;class Si extends En{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:e,motionValue:n,element:i,keyframes:s}=this.options,o=(null==i?void 0:i.KeyframeResolver)||un;this.resolver=new o(s,(t,e)=>this.onKeyframesResolved(t,e),e,n,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,r=H(e)?e:wi[e]||vi;let a,l;r!==vi&&"number"!=typeof t[0]&&(a=Bn(Pi,Nn(t[0],t[1])),t=[0,100]);const u=r({...this.options,keyframes:t});"mirror"===s&&(l=r({...this.options,keyframes:[...t].reverse(),velocity:-o})),null===u.calculatedDuration&&(u.calculatedDuration=X(u));const{calculatedDuration:c}=u,h=c+i;return{generator:u,mirroredGenerator:l,mapPercentToKeyframes:a,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(n+1)-i}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:r,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return s.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,m&&(n-=m/c)):"mirror"===p&&(x=o)),v=ie(0,1,n)*c}const w=y?{done:!1,value:a[0]}:x.next(v);r&&(w.value=r(w.value));let{done:P}=w;y||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return S&&void 0!==i&&(w.value=An(a,this.options,i)),f&&f(w.value),S&&this.finish(),w}get duration(){const{resolved:t}=this;return t?I(t.calculatedDuration):0}get time(){return I(this.currentTime)}set time(t){t=j(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=I(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=xi,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();const i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=i):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;this._resolved?(this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function Ti(t){return new Si(t)}const bi=new Set(["opacity","clipPath","filter","transform"]);function Ai(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=function t(e,n){return e?"function"==typeof e&&q()?Z(e,n):K(e)?Q(e):Array.isArray(e)?e.map(e=>t(e,n)||tt.easeOut):tt[e]:void 0}(a,s);Array.isArray(c)&&(u.easing=c);return t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}const Ei=B(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));const Mi={anticipate:_t,backInOut:Gt,circInOut:Jt};class Ci extends En{constructor(t){super(t);const{name:e,motionValue:n,element:i,keyframes:s}=this.options;this.resolver=new Pn(s,(t,e)=>this.onKeyframesResolved(t,e),e,n,i),this.resolver.scheduleResolve()}initPlayback(t,e){let{duration:n=300,times:i,ease:s,type:o,motionValue:r,name:a,startTime:l}=this.options;if(!r.owner||!r.owner.current)return!1;var u;if("string"==typeof s&&q()&&s in Mi&&(s=Mi[s]),H((u=this.options).type)||"spring"===u.type||!J(u.ease)){const{onComplete:e,onUpdate:r,motionValue:a,element:l,...u}=this.options,c=function(t,e){const n=new Si({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const s=[];let o=0;for(;!i.done&&o<2e4;)i=n.sample(o),s.push(i.value),o+=10;return{times:void 0,keyframes:s,duration:o-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),n=c.duration,i=c.times,s=c.ease,o="keyframes"}const c=Ai(r.owner.current,a,t,{...this.options,duration:n,times:i,ease:s});return c.startTime=null!=l?l:this.calcStartTime(),this.pendingTimeline?(Y(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:n}=this.options;r.set(An(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return I(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return I(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=j(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return R;const{animation:n}=e;Y(n,t)}else this.pendingTimeline=t;return R}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:i,type:s,ease:o,times:r}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){const{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,c=new Si({...u,keyframes:n,duration:i,type:s,ease:o,times:r,isGenerator:!0}),h=j(this.time);t.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Ei()&&n&&bi.has(n)&&!a&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}}const Vi={type:"spring",stiffness:500,damping:25,restSpeed:10},Ri={type:"keyframes",duration:.8},Di={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ki=(t,{keyframes:e})=>e.length>2?Ri:ee.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Vi:Di;const Li=(t,e,n,i={},s,o)=>r=>{const a=z(i,t)||{},l=a.delay||i.delay||0;let{elapsed:u=0}=i;u-=j(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||(c={...c,...ki(t,c)}),c.duration&&(c.duration=j(c.duration)),c.repeatDelay&&(c.repeatDelay=j(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if((!1===c.type||0===c.duration&&!c.repeatDelay)&&(c.duration=0,0===c.delay&&(h=!0)),(Nt.current||L.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,h&&!o&&void 0!==e.get()){const t=An(c.keyframes,a);if(void 0!==t)return st.update(()=>{c.onUpdate(t),c.onComplete()}),new N([])}return!o&&Ci.supports(c)?new Ci(c):new Si(c)};function Bi(t,e,n){const i=Wt(t)?t:Rt(t);return i.start(Li("",i,e,n)),i.animation}const Fi=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Oi="data-"+Fi("framerAppearId");function ji(t){return t.props[Oi]}function Ii(t){return t instanceof SVGElement&&"svg"!==t.tagName}const Ui=(t,e)=>t.depth-e.depth;class Wi{constructor(){this.children=[],this.isDirty=!1}add(t){C(this.children,t),this.isDirty=!0}remove(t){V(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Ui),this.isDirty=!1,this.children.forEach(t)}}function Ni(t,e){const n=dt.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(ot(i),t(o-e))};return st.read(i,!0),()=>ot(i)}const zi=t=>Array.isArray(t);function Xi(t){const e=Wt(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const $i=["TopLeft","TopRight","BottomLeft","BottomRight"],Hi=$i.length,Yi=t=>"string"==typeof t?parseFloat(t):t,Ki=t=>"number"==typeof t||ve.test(t);function Gi(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const _i=Zi(0,.5,Zt),qi=Zi(.5,.95,R);function Zi(t,e,n){return i=>i<t?0:i>e?1:n(F(t,e,i))}function Ji(t,e){t.min=e.min,t.max=e.max}function Qi(t,e){Ji(t.x,e.x),Ji(t.y,e.y)}function ts(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function es(t){return void 0===t||1===t}function ns({scale:t,scaleX:e,scaleY:n}){return!es(t)||!es(e)||!es(n)}function is(t){return ns(t)||ss(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function ss(t){return os(t.x)||os(t.y)}function os(t){return t&&"0%"!==t}function rs(t,e,n){return n+e*(t-n)}function as(t,e,n,i,s){return void 0!==s&&(t=rs(t,s,i)),rs(t,n,i)+e}function ls(t,e=0,n=1,i,s){t.min=as(t.min,e,n,i,s),t.max=as(t.max,e,n,i,s)}function us(t,{x:e,y:n}){ls(t.x,e.translate,e.scale,e.originPoint),ls(t.y,n.translate,n.scale,n.originPoint)}function cs(t,e){t.min=t.min+e,t.max=t.max+e}function hs(t,e,n,i,s=.5){ls(t,e,n,Dt(t.min,t.max,s),i)}function ds(t,e){hs(t.x,e.x,e.scaleX,e.scale,e.originX),hs(t.y,e.y,e.scaleY,e.scale,e.originY)}function ps(t,e,n,i,s){return t=rs(t-=e,1/n,i),void 0!==s&&(t=rs(t,1/s,i)),t}function ms(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){if(ye.test(e)){e=parseFloat(e);e=Dt(r.min,r.max,e/100)-r.min}if("number"!=typeof e)return;let a=Dt(o.min,o.max,i);t===o&&(a-=e),t.min=ps(t.min,e,n,a,s),t.max=ps(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const fs=["x","scaleX","originX"],gs=["y","scaleY","originY"];function ys(t,e,n,i){ms(t.x,e,fs,n?n.x:void 0,i?i.x:void 0),ms(t.y,e,gs,n?n.y:void 0,i?i.y:void 0)}const vs=()=>({x:{min:0,max:0},y:{min:0,max:0}});function xs(t){return 0===t.translate&&1===t.scale}function ws(t){return xs(t.x)&&xs(t.y)}function Ps(t,e){return t.min===e.min&&t.max===e.max}function Ss(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Ts(t,e){return Ss(t.x,e.x)&&Ss(t.y,e.y)}function bs(t){return kt(t.x)/kt(t.y)}function As(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Es{constructor(){this.members=[]}add(t){C(this.members,t),t.scheduleRender()}remove(t){if(V(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex(e=>t===e);if(0===e)return!1;let n;for(let t=e;t>=0;t--){const e=this.members[t];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const Ms={};function Cs(t){for(const e in t)Ms[e]=t[e],dn(e)&&(Ms[e].isCSSVariable=!0)}function Vs(t){return[t("x"),t("y")]}const Rs={hasAnimatedSinceResize:!0,hasEverUpdated:!1},Ds=["","X","Y","Z"],ks={visibility:"hidden"};let Ls=0;function Bs(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function Fs({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=Ls++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Is),this.nodes.forEach(Hs),this.nodes.forEach(Ys),this.nodes.forEach(Us)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new Wi)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new O),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Ii(e),this.instance=e;const{layoutId:i,layout:s,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(s||i)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=Ni(i,250),Rs.hasAnimatedSinceResize&&(Rs.hasAnimatedSinceResize=!1,this.nodes.forEach($s))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&o&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||o.getDefaultTransition()||Js,{onLayoutAnimationStart:r,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!Ts(this.targetLayout,i),u=!e&&n;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...z(s,"layout"),onPlay:r,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||$s(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,ot(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Ks),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:n}=e.options;if(!n)return;const i=ji(n);if(window.MotionHasOptimisedAnimation(i,"transform")){const{layout:t,layoutId:n}=e.options;window.MotionCancelOptimisedAnimation(i,"transform",st,!(t||n))}const{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){const e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Ns);this.isUpdating||this.nodes.forEach(zs),this.isUpdating=!1,this.nodes.forEach(Xs),this.nodes.forEach(Os),this.nodes.forEach(js),this.clearAllSnapshots();const t=dt.now();rt.delta=ie(0,1e3/60,t-rt.timestamp),rt.timestamp=t,rt.isProcessing=!0,at.update.process(rt),at.preRender.process(rt),at.render.process(rt),rt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,lt.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Ws),this.sharedNodes.forEach(Gs)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,st.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){st.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||kt(this.snapshot.measuredBox.x)||kt(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++){this.path[t].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ws(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||is(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),eo((i=n).x),eo(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(io))){const{scroll:t}=this.root;t&&(cs(n.x,t.offset.x),cs(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(Qi(n,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return n;for(let e=0;e<this.path.length;e++){const i=this.path[e],{scroll:s,options:o}=i;i!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Qi(n,t),cs(n.x,s.offset.x),cs(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Qi(n,t);for(let t=0;t<this.path.length;t++){const i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ds(n,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),is(i.latestValues)&&ds(n,i.latestValues)}return is(this.latestValues)&&ds(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Qi(e,t);for(let t=0;t<this.path.length;t++){const n=this.path[t];if(!n.instance)continue;if(!is(n.latestValues))continue;ns(n.latestValues)&&n.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Qi(i,n.measurePageBox()),ys(e,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,i)}return is(this.latestValues)&&ys(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==rt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=rt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},jt(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Qi(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var r,a,l;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,Ft(r.x,a.x,l.x),Ft(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Qi(this.target,this.layout.layoutBox),us(this.target,this.targetDelta)):Qi(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},jt(this.relativeTargetOrigin,this.target,t.target),Qi(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!ns(this.parent.latestValues)&&!ss(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===rt.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Qi(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ds(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,us(t,r)),i&&is(o.latestValues)&&ds(t,o.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(ts(this.prevProjectionDelta.x,this.projectionDelta.x),ts(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Bt(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&As(this.projectionDelta.x,this.prevProjectionDelta.x)&&As(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Zs));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d;_s(o.x,t.x,n),_s(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(jt(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(t,e,n,i){qs(t.x,e.x,n.x,i),qs(t.y,e.y,n.y,i)}(this.relativeTarget,this.relativeTargetOrigin,r,n),h&&(l=this.relativeTarget,d=h,Ps(l.x,d.x)&&Ps(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),Qi(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Dt(0,void 0!==n.opacity?n.opacity:1,_i(i)),t.opacityExit=Dt(void 0!==e.opacity?e.opacity:1,0,qi(i))):o&&(t.opacity=Dt(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let s=0;s<Hi;s++){const o=`border${$i[s]}Radius`;let r=Gi(e,o),a=Gi(n,o);if(void 0===r&&void 0===a)continue;r||(r=0),a||(a=0);0===r||0===a||Ki(r)===Ki(a)?(t[o]=Math.max(Dt(Yi(r),Yi(a),i),0),(ye.test(a)||ye.test(r))&&(t[o]+="%")):t[o]=a}(e.rotate||n.rotate)&&(t.rotate=Dt(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(ot(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=st.update(()=>{Rs.hasAnimatedSinceResize=!0,this.currentAnimation=Bi(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&no(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=kt(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=kt(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Qi(e,n),ds(e,s),Bt(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Es);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&Bs("z",t,i,this.animationValues);for(let e=0;e<Ds.length;e++)Bs("rotate"+Ds[e],t,i,this.animationValues),Bs("skew"+Ds[e],t,i,this.animationValues);t.render();for(const e in i)t.setStaticValue(e,i[e]),this.animationValues&&(this.animationValues[e]=i[e]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return ks;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=Xi(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Xi(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!is(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const t in Ms){if(void 0===r[t])continue;const{correct:e,applyTo:n,isCSSVariable:s}=Ms[t],a="none"===i.transform?r[t]:e(r[t],o);if(n){const t=n.length;for(let e=0;e<t;e++)i[n[e]]=a}else s?this.options.visualElement.renderState.vars[t]=a:i[t]=a}return this.options.layoutId&&(i.pointerEvents=o===this?Xi(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(Ns),this.root.sharedNodes.clear()}}}function Os(t){t.updateLayout()}function js(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?Vs(t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=kt(i);i.min=e[t].min,i.max=i.min+s}):no(s,n.layoutBox,e)&&Vs(i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=kt(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)});const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Bt(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Bt(a,t.applyTransform(i,!0),n.measuredBox):Bt(a,e,n.layoutBox);const l=!ws(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};jt(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};jt(a,e,o.layoutBox),Ts(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function Is(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Us(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Ws(t){t.clearSnapshot()}function Ns(t){t.clearMeasurements()}function zs(t){t.isLayoutDirty=!1}function Xs(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function $s(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Hs(t){t.resolveTargetDelta()}function Ys(t){t.calcProjection()}function Ks(t){t.resetSkewAndRotation()}function Gs(t){t.removeLeadSnapshot()}function _s(t,e,n){t.translate=Dt(e.translate,0,n),t.scale=Dt(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function qs(t,e,n,i){t.min=Dt(e.min,n.min,i),t.max=Dt(e.max,n.max,i)}function Zs(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Js={duration:.45,ease:[.4,0,.1,1]},Qs=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),to=Qs("applewebkit/")&&!Qs("chrome/")?Math.round:R;function eo(t){t.min=to(t.min),t.max=to(t.max)}function no(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=bs(e),s=bs(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function io(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}function so(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}const oo=Fs({attachResizeListener:(t,e)=>so(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ro={current:void 0},ao=Fs({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ro.current){const t=new oo({});t.mount(window),t.setOptions({layoutScroll:!0}),ro.current=t}return ro.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)});function lo(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const uo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!ve.test(t))return t;t=parseFloat(t)}return`${lo(t,e.target.x)}% ${lo(t,e.target.y)}%`}},co={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Re.parse(t);if(s.length>5)return i;const o=Re.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Dt(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};function ho({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function po(t,e){return ho(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const mo={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},fo={};for(const t in mo)fo[t]={isEnabled:e=>mo[t].some(t=>!!e[t])};const go={current:null},yo={current:!1};function vo(){if(yo.current=!0,g)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>go.current=t.matches;t.addListener(e),e()}else go.current=!1}const xo=[...xn,Te,Re],wo=new WeakMap;function Po(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function So(t){return"string"==typeof t||Array.isArray(t)}const To=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],bo=["initial",...To];function Ao(t){return Po(t.animate)||bo.some(e=>So(t[e]))}function Eo(t){return Boolean(Ao(t)||t.variants)}function Mo(t){const e=[{},{}];return null==t||t.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function Co(t,e,n,i){if("function"==typeof e){const[s,o]=Mo(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=Mo(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const Vo=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Ro{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=un,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=dt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,st.render(this.render,!1,!0))};const{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=Ao(e),this.isVariantNode=Eo(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==a[t]&&Wt(e)&&e.set(a[t],!1)}}mount(t){this.current=t,wo.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),yo.current||vo(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||go.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),ot(this.notifyUpdate),ot(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=ee.has(t);n&&this.onBindTransform&&this.onBindTransform();const i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&st.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in fo){const e=fo[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Vo.length;e++){const n=Vo[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const i=t["on"+n];i&&(this.propEventSubscriptions[n]=this.on(n,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(Wt(s))t.addValue(i,s);else if(Wt(o))t.addValue(i,Rt(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Rt(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Rt(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){var n;let i=void 0===this.latestValues[t]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,t))&&void 0!==n?n:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&(cn(i)||Qt(i))?i=parseFloat(i):(s=i,!xo.find(vn(s))&&Re.test(e)&&(i=Ne(t,e))),this.setBaseTarget(t,Wt(i)?i.get():i)),Wt(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=Co(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Wt(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new O),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Do extends Ro{constructor(){super(...arguments),this.KeyframeResolver=Pn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Wt(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=""+t)}))}}const ko=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Lo={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Bo=te.length;function Fo(t,e,n){let i="",s=!0;for(let o=0;o<Bo;o++){const r=te[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=ko(a,Ie[r]);if(!l){s=!1;i+=`${Lo[r]||r}(${t}) `}n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}function Oo(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const t in e){const n=e[t];if(ee.has(t))r=!0;else if(dn(t))s[t]=n;else{const e=ko(n,Ie[t]);t.startsWith("origin")?(a=!0,o[t]=e):i[t]=e}}if(e.transform||(r||n?i.transform=Fo(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}function jo(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const e in n)t.style.setProperty(e,n[e])}function Io(t,{layout:e,layoutId:n}){return ee.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Ms[t]||"opacity"===t)}function Uo(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(Wt(s[r])||e.style&&Wt(e.style[r])||Io(r,t)||void 0!==(null===(i=null==n?void 0:n.getValue(r))||void 0===i?void 0:i.liveStyle))&&(o[r]=s[r]);return o}class Wo extends Do{constructor(){super(...arguments),this.type="html",this.renderInstance=jo}readValueFromInstance(t,e){if(ee.has(e))return((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Ze(n,e)})(t,e);{const i=(n=t,window.getComputedStyle(n)),s=(dn(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return po(t,e)}build(t,e,n){Oo(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return Uo(t,e,n)}}function No(){const t=function(){const t=e.useRef(!1);return y(()=>(t.current=!0,()=>{t.current=!1}),[]),t}(),[n,i]=e.useState(0),s=e.useCallback(()=>{t.current&&i(n+1)},[n]);return[e.useCallback(()=>st.postRender(s),[s]),n]}const zo=t=>!0===t,Xo=({children:t,id:n,inherit:i=!0})=>{const s=e.useContext(m),o=e.useContext(M),[r,a]=No(),l=e.useRef(null),u=s.id||o;null===l.current&&((t=>zo(!0===t)||"id"===t)(i)&&u&&(n=n?u+"-"+n:u),l.current={id:n,group:zo(i)&&s.group||Ut()});const c=e.useMemo(()=>({...l.current,forceRender:r}),[a]);return d(m.Provider,{value:c,children:t})},$o=e.createContext({strict:!1});function Ho(t){for(const e in t)fo[e]={...fo[e],...t[e]}}function Yo(t){return"function"==typeof t}const Ko=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Go(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Ko.has(t)}let _o=t=>!Go(t);function qo(t){t&&(_o=e=>e.startsWith("on")?!Go(e):t(e))}try{qo(require("@emotion/is-prop-valid").default)}catch(t){}function Zo(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(_o(s)||!0===n&&Go(s)||!e&&!Go(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}const Jo=e.createContext(null);function Qo(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy((...e)=>t(...e),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}function tr(t,e,n){const i=t.getProps();return Co(i,e,void 0!==n?n:i.custom,t)}function er(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Rt(n))}function nr(t,e){const n=tr(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const e in o){er(t,e,(r=o[e],zi(r)?r[r.length-1]||0:r))}var r}function ir(t,e){const n=t.getValue("willChange");if(i=n,Boolean(Wt(i)&&i.add))return n.add(e);var i}function sr({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function or(t,e,{delay:n=0,transitionOverride:i,type:s}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;i&&(r=i);const u=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const e in l){const i=t.getValue(e,null!==(o=t.latestValues[e])&&void 0!==o?o:null),s=l[e];if(void 0===s||c&&sr(c,e))continue;const a={delay:n,...z(r||{},e)};let h=!1;if(window.MotionHandoffAnimation){const n=ji(t);if(n){const t=window.MotionHandoffAnimation(n,e,st);null!==t&&(a.startTime=t,h=!0)}}ir(t,e),i.start(Li(e,i,s,t.shouldReduceMotion&&ne.has(e)?{type:!1}:a,t,h));const d=i.animation;d&&u.push(d)}return a&&Promise.all(u).then(()=>{st.update(()=>{a&&nr(t,a)})}),u}function rr(t,e,n={}){var i;const s=tr(t,e,"exit"===n.type?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(or(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(ar).forEach((t,i)=>{t.notify("AnimationStart",e),r.push(rr(t,e,{...o,delay:n+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}return Promise.all([r(),a(n.delay)])}function ar(t,e){return t.sortNodePosition(e)}function lr(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map(e=>rr(t,e,n));i=Promise.all(s)}else if("string"==typeof e)i=rr(t,e,n);else{const s="function"==typeof e?tr(t,e,n.custom):e;i=Promise.all(or(t,s,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}function ur(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}const cr=bo.length;const hr=[...To].reverse(),dr=To.length;function pr(t){let e=function(t){return e=>Promise.all(e.map(({animation:e,options:n})=>lr(t,e,n)))}(t),n=gr(),i=!0;const s=e=>(n,i)=>{var s;const o=tr(t,i,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(n.initial=e.props.initial),n}const n={};for(let t=0;t<cr;t++){const i=bo[t],s=e.props[i];(So(s)||!1===s)&&(n[i]=s)}return n}(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<dr;e++){const d=hr[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=So(m),g=d===o?p.isActive:null;!1===g&&(h=e);let y=m===a[d]&&m!==r[d]&&f;if(y&&i&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||Po(m)||"boolean"==typeof m)continue;const v=mr(p.prevProp,m);let x=v||d===o&&p.isActive&&!y&&f||e>h&&f,w=!1;const P=Array.isArray(m)?m:[m];let S=P.reduce(s(d),{});!1===g&&(S={});const{prevResolvedValues:T={}}=p,b={...T,...S},A=e=>{x=!0,u.has(e)&&(w=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=S[t],n=T[t];if(c.hasOwnProperty(t))continue;let i=!1;i=zi(e)&&zi(n)?!ur(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(c={...c,...S}),i&&t.blockInitialAnimation&&(x=!1);const E=!(y&&v)||w;x&&E&&l.push(...P.map(t=>({animation:t,options:{type:d}})))}if(u.size){const e={};if("boolean"!=typeof r.initial){const n=tr(t,Array.isArray(r.initial)?r.initial[0]:r.initial);n&&n.transition&&(e.transition=n.transition)}u.forEach(n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=null!=i?i:null}),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)}),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=gr(),i=!0}}}function mr(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!ur(e,t)}function fr(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function gr(){return{animate:fr(!0),whileInView:fr(),whileHover:fr(),whileTap:fr(),whileDrag:fr(),whileFocus:fr(),exit:fr()}}class yr{constructor(t){this.isMounted=!1,this.node=t}update(){}}let vr=0;const xr={animation:{Feature:class extends yr{constructor(t){super(t),t.animationState||(t.animationState=pr(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Po(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}},exit:{Feature:class extends yr{constructor(){super(...arguments),this.id=vr++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then(()=>{e(this.id)})}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function wr(t){return{point:{x:t.pageX,y:t.pageY}}}const Pr=t=>e=>Pt(e)&&t(e,wr(e));function Sr(t,e,n,i){return so(t,e,Pr(n),i)}const Tr=({current:t})=>t?t.ownerDocument.defaultView:null;function br(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const Ar=(t,e)=>Math.abs(t-e);function Er(t,e){const n=Ar(t.x,e.x),i=Ar(t.y,e.y);return Math.sqrt(n**2+i**2)}class Mr{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Rr(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=Er(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=rt;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Cr(e,this.transformPagePoint),st.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Rr("pointercancel"===t.type?this.lastMoveEventInfo:Cr(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!Pt(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=Cr(wr(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=rt;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Rr(o,this.history)),this.removeListeners=Bn(Sr(this.contextWindow,"pointermove",this.handlePointerMove),Sr(this.contextWindow,"pointerup",this.handlePointerUp),Sr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),ot(this.updatePoint)}}function Cr(t,e){return e?{point:e(t.point)}:t}function Vr(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Rr({point:t},e){return{point:t,delta:Vr(t,kr(e)),offset:Vr(t,Dr(e)),velocity:Lr(e,.1)}}function Dr(t){return t[0]}function kr(t){return t[t.length-1]}function Lr(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const s=kr(t);for(;n>=0&&(i=t[n],!(s.timestamp-i.timestamp>j(e)));)n--;if(!i)return{x:0,y:0};const o=I(s.timestamp-i.timestamp);if(0===o)return{x:0,y:0};const r={x:(s.x-i.x)/o,y:(s.y-i.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Br(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Fr(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Or=.35;function jr(t,e,n){return{min:Ir(t,e),max:Ir(t,n)}}function Ir(t,e){return"number"==typeof t?t:t[e]||0}const Ur=new WeakMap;class Wr{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new Mr(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(wr(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(o=n)||"y"===o?pt[o]?null:(pt[o]=!0,()=>{pt[o]=!1}):pt.x||pt.y?null:(pt.x=pt.y=!0,()=>{pt.x=pt.y=!1}),!this.openDragLock))return;var o;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Vs(t=>{let e=this.getAxisMotionValue(t).get()||0;if(ye.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=kt(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e}),s&&st.postRender(()=>s(t,e)),ir(this.visualElement,"transform");const{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>Vs(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:Tr(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&st.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!Nr(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Dt(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Dt(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&br(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Br(t.x,n,s),y:Br(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Or){return!1===t?t=0:!0===t&&(t=Or),{x:jr(t,"left","right"),y:jr(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Vs(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!br(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=po(t,n),{scroll:s}=e;return s&&(cs(i.x,s.offset.x),cs(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Fr(t.x,e.x),y:Fr(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ho(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=Vs(r=>{if(!Nr(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)});return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return ir(this.visualElement,t),n.start(Li(t,n,0,e,this.visualElement,!1))}stopAnimation(){Vs(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Vs(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e="_drag"+t.toUpperCase(),n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){Vs(e=>{const{drag:n}=this.getProps();if(!Nr(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Dt(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!br(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Vs(t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=kt(t),s=kt(e);return s>i?n=F(e.min,e.max-i,t.min):i>s&&(n=F(t.min,t.max-s,e.min)),ie(0,1,n)}({min:n,max:n},this.constraints[t])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Vs(e=>{if(!Nr(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Dt(s,o,i[e]))})}addListeners(){if(!this.visualElement.current)return;Ur.set(this.visualElement,this);const t=Sr(this.visualElement.current,"pointerdown",t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)}),e=()=>{const{dragConstraints:t}=this.getProps();br(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),st.read(e);const s=so(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(Vs(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Or,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function Nr(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const zr=t=>(e,n)=>{t&&st.postRender(()=>t(e,n))};const Xr=e.createContext({});class $r extends e.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;Cs(Yr),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Rs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e||t.isPresent!==s?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||st.postRender(()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),lt.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Hr(t){const[n,i]=b(),s=e.useContext(m);return d($r,{...t,layoutGroup:s,switchLayoutGroup:e.useContext(Xr),isPresent:n,safeToRemove:i})}const Yr={borderRadius:{...uo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:uo,borderTopRightRadius:uo,borderBottomLeftRadius:uo,borderBottomRightRadius:uo,boxShadow:co},Kr={pan:{Feature:class extends yr{constructor(){super(...arguments),this.removePointerDownListener=R}onPointerDown(t){this.session=new Mr(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Tr(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:zr(t),onStart:zr(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&st.postRender(()=>i(t,e))}}}mount(){this.removePointerDownListener=Sr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends yr{constructor(t){super(t),this.removeGroupControls=R,this.removeListeners=R,this.controls=new Wr(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||R}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:ao,MeasureLayout:Hr}};function Gr(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&st.postRender(()=>s(e,wr(e)))}function _r(t,e,n){const{props:i}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&st.postRender(()=>s(e,wr(e)))}const qr=new WeakMap,Zr=new WeakMap,Jr=t=>{const e=qr.get(t.target);e&&e(t)},Qr=t=>{t.forEach(Jr)};function ta(t,e,n){const i=function({root:t,...e}){const n=t||document;Zr.has(n)||Zr.set(n,{});const i=Zr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Qr,{root:t,...e})),i[s]}(e);return qr.set(t,n),i.observe(t),()=>{qr.delete(t),i.unobserve(t)}}const ea={some:0,all:1};const na={inView:{Feature:class extends yr{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:ea[i]};return ta(this.node.current,o,t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends yr{mount(){const{current:t}=this.node;t&&(this.unmount=Mt(t,(t,e)=>(_r(this.node,e,"Start"),(t,{success:e})=>_r(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends yr{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Bn(so(this.node.current,"focus",()=>this.onFocus()),so(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends yr{mount(){const{current:t}=this.node;t&&(this.unmount=vt(t,(t,e)=>(Gr(this.node,e,"Start"),t=>Gr(this.node,t,"End"))))}unmount(){}}}},ia={layout:{ProjectionNode:ao,MeasureLayout:Hr}},sa=e.createContext({});function oa(t){const{initial:n,animate:i}=function(t,e){if(Ao(t)){const{initial:e,animate:n}=t;return{initial:!1===e||So(e)?e:void 0,animate:So(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,e.useContext(sa));return e.useMemo(()=>({initial:n,animate:i}),[ra(n),ra(i)])}function ra(t){return Array.isArray(t)?t.join(" "):t}const aa=Symbol.for("motionComponentSymbol");function la(t,n,i){return e.useCallback(e=>{e&&t.onMount&&t.onMount(e),n&&(e?n.mount(e):n.unmount()),i&&("function"==typeof i?i(e):br(i)&&(i.current=e))},[n])}function ua(t,n,i,s,o){var r,a;const{visualElement:l}=e.useContext(sa),u=e.useContext($o),c=e.useContext(v),h=e.useContext(x).reducedMotion,d=e.useRef(null);s=s||u.renderer,!d.current&&s&&(d.current=s(t,{visualState:n,parent:l,props:i,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:h}));const p=d.current,m=e.useContext(Xr);!p||p.projection||!o||"html"!==p.type&&"svg"!==p.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){return e?!1!==e.options.allowProjection?e.projection:t(e.parent):void 0}(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&br(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:u})}(d.current,i,o,m);const f=e.useRef(!1);e.useInsertionEffect(()=>{p&&f.current&&p.update(i,c)});const g=i[Oi],w=e.useRef(Boolean(g)&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,g))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,g)));return y(()=>{p&&(f.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),lt.render(p.render),w.current&&p.animationState&&p.animationState.animateChanges())}),e.useEffect(()=>{p&&(!w.current&&p.animationState&&p.animationState.animateChanges(),w.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,g)}),w.current=!1))}),p}function ca({preloadedFeatures:t,createVisualElement:n,useRender:i,useVisualState:s,Component:o}){var r,a;function l(t,r){let a;const l={...e.useContext(x),...t,layoutId:ha(t)},{isStatic:u}=l,c=oa(t),h=s(t,u);if(!u&&g){e.useContext($o).strict;const t=function(t){const{drag:e,layout:n}=fo;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(l);a=t.MeasureLayout,c.visualElement=ua(o,h,l,n,t.ProjectionNode)}return p(sa.Provider,{value:c,children:[a&&c.visualElement?d(a,{visualElement:c.visualElement,...l}):null,i(o,t,la(h,c.visualElement,r),h,u,c.visualElement)]})}t&&Ho(t),l.displayName="motion."+("string"==typeof o?o:`create(${null!==(a=null!==(r=o.displayName)&&void 0!==r?r:o.name)&&void 0!==a?a:""})`);const u=e.forwardRef(l);return u[aa]=o,u}function ha({layoutId:t}){const n=e.useContext(m).id;return n&&void 0!==t?n+"-"+t:t}const da=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function pa(t,e,n){for(const i in e)Wt(e[i])||Io(i,n)||(t[i]=e[i])}function ma(t,n){const i={};return pa(i,t.style||{},t),Object.assign(i,function({transformTemplate:t},n){return e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return Oo(e,n,t),Object.assign({},e.vars,e.style)},[n])}(t,n)),i}function fa(t,e){const n={},i=ma(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}const ga=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ya(t){return"string"==typeof t&&!t.includes("-")&&!!(ga.indexOf(t)>-1||/[A-Z]/u.test(t))}const va={offset:"stroke-dashoffset",array:"stroke-dasharray"},xa={offset:"strokeDashoffset",array:"strokeDasharray"};function wa(t,e,n){return"string"==typeof t?t:ve.transform(e+n*t)}function Pa(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(Oo(t,u,h),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==s||void 0!==o||p.transform)&&(p.transformOrigin=function(t,e,n){return`${wa(e,t.x,t.width)} ${wa(n,t.y,t.height)}`}(m,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==i&&(d.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?va:xa;t[o.offset]=ve.transform(-i);const r=ve.transform(e),a=ve.transform(n);t[o.array]=`${r} ${a}`}(d,r,a,l,!1)}const Sa=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),Ta=t=>"string"==typeof t&&"svg"===t.toLowerCase();function ba(t,n,i,s){const o=e.useMemo(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return Pa(e,n,Ta(s),t.transformTemplate),{...e.attrs,style:{...e.style}}},[n]);if(t.style){const e={};pa(e,t.style,t),o.style={...e,...o.style}}return o}function Aa(t=!1){return(n,i,s,{latestValues:o},r)=>{const a=(ya(n)?ba:fa)(i,o,r,n),l=Zo(i,"string"==typeof n,t),u=n!==e.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=e.useMemo(()=>Wt(c)?c.get():c,[c]);return e.createElement(n,{...u,children:h})}}const Ea=t=>(n,i)=>{const s=e.useContext(sa),o=e.useContext(v),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},i,s,o){const r={latestValues:Ma(i,s,o,t),renderState:e()};return n&&(r.onMount=t=>n({props:i,current:t,...r}),r.onUpdate=t=>n(t)),r}(t,n,s,o);return i?r():f(r)};function Ma(t,e,n,i){const s={},o=i(t,{});for(const t in o)s[t]=Xi(o[t]);let{initial:r,animate:a}=t;const l=Ao(t),u=Eo(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!Po(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=Co(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[c?e.length-1:0]}null!==e&&(s[t]=e)}for(const e in t)s[e]=t[e]}}}return s}const Ca={useVisualState:Ea({scrapeMotionValuesFromProps:Uo,createRenderState:da})};function Va(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}const Ra=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Da(t,e,n,i){jo(t,e,void 0,i);for(const n in e.attrs)t.setAttribute(Ra.has(n)?n:Fi(n),e.attrs[n])}function ka(t,e,n){const i=Uo(t,e,n);for(const n in t)if(Wt(t[n])||Wt(e[n])){i[-1!==te.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return i}const La=["x","y","width","height","cx","cy","r"],Ba={useVisualState:Ea({scrapeMotionValuesFromProps:ka,createRenderState:Sa,onUpdate:({props:t,prevProps:e,current:n,renderState:i,latestValues:s})=>{if(!n)return;let o=!!t.drag;if(!o)for(const t in s)if(ee.has(t)){o=!0;break}if(!o)return;let r=!e;if(e)for(let n=0;n<La.length;n++){const i=La[n];t[i]!==e[i]&&(r=!0)}r&&st.read(()=>{Va(n,i),st.render(()=>{Pa(i,s,Ta(n.tagName),t.transformTemplate),Da(n,i)})})}})};function Fa(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return ca({...ya(n)?Ba:Ca,preloadedFeatures:t,useRender:Aa(i),createVisualElement:e,Component:n})}}class Oa extends Do{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=vs,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&Va(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(ee.has(e)){const t=We(e);return t&&t.default||0}return e=Ra.has(e)?e:Fi(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return ka(t,e,n)}onBindTransform(){this.current&&!this.renderState.dimensions&&st.postRender(this.updateDimensions)}build(t,e,n){Pa(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,i){Da(t,e,0,i)}mount(t){this.isSVGTag=Ta(t.tagName),super.mount(t)}}const ja=(t,n)=>ya(t)?new Oa(n):new Wo(n,{allowProjection:t!==e.Fragment}),Ia=Qo(Fa({...xr,...na,...Kr,...ia},ja));function Ua({children:t,as:n="ul",axis:i="y",onReorder:s,values:o,...r},a){const l=f(()=>Ia[n]),u=[],c=e.useRef(!1),h={axis:i,registerItem:(t,e)=>{const n=u.findIndex(e=>t===e.value);-1!==n?u[n].layout=e[i]:u.push({value:t,layout:e[i]}),u.sort(za)},updateOrder:(t,e,n)=>{if(c.current)return;const i=function(t,e,n,i){if(!i)return t;const s=t.findIndex(t=>t.value===e);if(-1===s)return t;const o=i>0?1:-1,r=t[s+o];if(!r)return t;const a=t[s],l=r.layout,u=Dt(l.min,l.max,.5);return 1===o&&a.layout.max+n>u||-1===o&&a.layout.min+n<u?function([...t],e,n){const i=e<0?t.length+e:e;if(i>=0&&i<t.length){const i=n<0?t.length+n:n,[s]=t.splice(e,1);t.splice(i,0,s)}return t}(t,s,s+o):t}(u,t,e,n);u!==i&&(c.current=!0,s(i.map(Na).filter(t=>-1!==o.indexOf(t))))}};return e.useEffect(()=>{c.current=!1}),d(l,{...r,ref:a,ignoreStrict:!0,children:d(Jo.Provider,{value:h,children:t})})}const Wa=e.forwardRef(Ua);function Na(t){return t.value}function za(t,e){return t.layout.min-e.layout.min}function Xa(t){const n=f(()=>Rt(t)),{isStatic:i}=e.useContext(x);if(i){const[,i]=e.useState(t);e.useEffect(()=>n.on("change",i),[])}return n}function $a(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=t[1+n],o=t[2+n],r=t[3+n],a=fi(s,o,{mixer:(l=o[0],(t=>t&&"object"==typeof t&&t.mix)(l)?l.mix:void 0),...r});var l;return e?a(i):a}function Ha(t,e){const n=Xa(e()),i=()=>n.set(e());return i(),y(()=>{const e=()=>st.preRender(i,!1,!0),n=t.map(t=>t.on("change",e));return()=>{n.forEach(t=>t()),ot(i)}}),n}function Ya(t,e,n,i){if("function"==typeof t)return function(t){Ct.current=[],t();const e=Ha(Ct.current,t);return Ct.current=void 0,e}(t);const s="function"==typeof e?e:$a(e,n,i);return Array.isArray(t)?Ka(t,s):Ka([t],([t])=>s(t))}function Ka(t,e){const n=f(()=>[]);return Ha(t,()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)})}function Ga(t,e=0){return Wt(t)?t:Xa(e)}function _a({children:t,style:n={},value:i,as:s="li",onDrag:o,layout:r=!0,...a},l){const u=f(()=>Ia[s]),c=e.useContext(Jo),h={x:Ga(n.x),y:Ga(n.y)},p=Ya([h.x,h.y],([t,e])=>t||e?1:"unset"),{axis:m,registerItem:g,updateOrder:y}=c;return d(u,{drag:m,...a,dragSnapToOrigin:!0,style:{...n,x:h.x,y:h.y,zIndex:p},layout:r,onDrag:(t,e)=>{const{velocity:n}=e;n[m]&&y(i,h[m].get(),n[m]),o&&o(t,e)},onLayoutMeasure:t=>g(i,t),ref:l,ignoreStrict:!0,children:t})}const qa=e.forwardRef(_a);var Za=Object.freeze({__proto__:null,Group:Wa,Item:qa});const Ja=(t,e,n)=>{const i=e-t;return((n-t)%i+i)%i+t};function Qa(t,e){return di(t)?t[Ja(0,t.length,e)]:t}function tl(t){return"object"==typeof t&&!Array.isArray(t)}function el(t,e,n,i){return"string"==typeof t&&tl(e)?ft(t,n,i):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function nl(t,e,n){return t*(e+1)}function il(t,e,n,i){var s;return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:null!==(s=i.get(e))&&void 0!==s?s:t}function sl(t,e,n,i,s,o){!function(t,e,n){for(let i=0;i<t.length;i++){const s=t[i];s.at>e&&s.at<n&&(V(t,s),i--)}}(t,s,o);for(let r=0;r<e.length;r++)t.push({value:e[r],at:Dt(s,o,i[r]),easing:Qa(n,r)})}function ol(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function rl(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function al(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function ll(t,e){return e[t]||(e[t]=[]),e[t]}function ul(t){return Array.isArray(t)?t:[t]}function cl(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const hl=t=>"number"==typeof t,dl=t=>t.every(hl);class pl extends Ro{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function ml(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=Ii(t)?new Oa(e):new Wo(e);n.mount(t),wo.set(t,n)}function fl(t){const e=new pl({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),wo.set(t,e)}function gl(t,e,n,i){const s=[];if(function(t,e){return Wt(t)||"number"==typeof t||"string"==typeof t&&!tl(e)}(t,e))s.push(Bi(t,tl(e)&&e.default||e,n&&n.default||n));else{const o=el(t,e,i),r=o.length;for(let t=0;t<r;t++){const i=o[t],a=i instanceof Element?ml:fl;wo.has(i)||a(i);const l=wo.get(i),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,r)),s.push(...or(l,{...e,transition:u},{}))}}return s}function yl(t,e,n){const i=[];return function(t,{defaultTransition:e={},...n}={},i,s){const o=e.duration||.3,r=new Map,a=new Map,l={},u=new Map;let c=0,h=0,d=0;for(let n=0;n<t.length;n++){const r=t[n];if("string"==typeof r){u.set(r,h);continue}if(!Array.isArray(r)){u.set(r.name,il(h,r.at,c,u));continue}let[p,m,f={}]=r;void 0!==f.at&&(h=il(h,f.at,c,u));let g=0;const y=(t,n,i,r=0,a=0)=>{const l=ul(t),{delay:u=0,times:c=yi(l),type:p="keyframes",repeat:m,repeatType:f,repeatDelay:y=0,...v}=n;let{ease:x=e.ease||"easeOut",duration:w}=n;const P="function"==typeof u?u(r,a):u,S=l.length,T=H(p)?p:null==s?void 0:s[p];if(S<=2&&T){let t=100;if(2===S&&dl(l)){const e=l[1]-l[0];t=Math.abs(e)}const e={...v};void 0!==w&&(e.duration=j(w));const n=$(e,t,T);x=n.ease,w=n.duration}null!=w||(w=o);const b=h+P;1===c.length&&0===c[0]&&(c[1]=1);const A=c.length-l.length;if(A>0&&gi(c,A),1===l.length&&l.unshift(null),m){w=nl(w,m);const t=[...l],e=[...c];x=Array.isArray(x)?[...x]:[x];const n=[...x];for(let i=0;i<m;i++){l.push(...t);for(let s=0;s<t.length;s++)c.push(e[s]+(i+1)),x.push(0===s?"linear":Qa(n,s-1))}ol(c,m)}const E=b+w;sl(i,l,x,c,b,E),g=Math.max(P+w,g),d=Math.max(E,d)};if(Wt(p)){y(m,f,ll("default",al(p,a)))}else{const t=el(p,m,i,l),e=t.length;for(let n=0;n<e;n++){m=m,f=f;const i=al(t[n],a);for(const t in m)y(m[t],cl(f,t),ll(t,i),n,e)}}c=h,h+=g}return a.forEach((t,i)=>{for(const s in t){const o=t[s];o.sort(rl);const a=[],l=[],u=[];for(let t=0;t<o.length;t++){const{at:e,value:n,easing:i}=o[t];a.push(n),l.push(F(0,d,e)),u.push(i||"easeOut")}0!==l[0]&&(l.unshift(0),a.unshift(a[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),a.push(null)),r.has(i)||r.set(i,{keyframes:{},transition:{}});const c=r.get(i);c.keyframes[s]=a,c.transition[s]={...e,duration:d,ease:u,times:l,...n}}}),r}(t,e,n,{spring:ai}).forEach(({keyframes:t,transition:e},n)=>{i.push(...gl(n,t,e))}),i}function vl(t){return function(e,n,i){let s=[];var o;o=e,s=Array.isArray(o)&&o.some(Array.isArray)?yl(e,n,t):gl(e,n,i,t);const r=new N(s);return t&&t.animations.push(r),r}}const xl=vl();function wl(t,e,n){t.style.setProperty(e,n)}function Pl(t,e,n){t.style[e]=n}const Sl=B(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),Tl=new WeakMap;function bl(t){const e=Tl.get(t)||new Map;return Tl.set(t,e),Tl.get(t)}class Al extends class{constructor(t){this.animation=t}get duration(){var t,e,n;const i=(null===(e=null===(t=this.animation)||void 0===t?void 0:t.effect)||void 0===e?void 0:e.getComputedTiming().duration)||(null===(n=this.options)||void 0===n?void 0:n.duration)||300;return I(Number(i))}get time(){var t;return this.animation?I((null===(t=this.animation)||void 0===t?void 0:t.currentTime)||0):0}set time(t){this.animation&&(this.animation.currentTime=j(t))}get speed(){return this.animation?this.animation.playbackRate:1}set speed(t){this.animation&&(this.animation.playbackRate=t)}get state(){return this.animation?this.animation.playState:"finished"}get startTime(){return this.animation?this.animation.startTime:null}get finished(){return this.animation?this.animation.finished:Promise.resolve()}play(){this.animation&&this.animation.play()}pause(){this.animation&&this.animation.pause()}stop(){this.animation&&"idle"!==this.state&&"finished"!==this.state&&(this.animation.commitStyles&&this.animation.commitStyles(),this.cancel())}flatten(){var t,e;this.animation&&(null===(t=this.options)||void 0===t?void 0:t.allowFlatten)&&(null===(e=this.animation.effect)||void 0===e||e.updateTiming({easing:"linear"}))}attachTimeline(t){return this.animation&&Y(this.animation,t),R}complete(){this.animation&&this.animation.finish()}cancel(){try{this.animation&&this.animation.cancel()}catch(t){}}}{constructor(t,e,n,i){const s=e.startsWith("--");k("string"!=typeof i.type);const o=bl(t).get(e);o&&o.stop();if(Array.isArray(n)||(n=[n]),function(t,e,n){for(let i=0;i<e.length;i++)null===e[i]&&(e[i]=0===i?n():e[i-1]),"number"==typeof e[i]&&Fe[t]&&(e[i]=Fe[t].transform(e[i]));!Sl()&&e.length<2&&e.unshift(n())}(e,n,()=>e.startsWith("--")?t.style.getPropertyValue(e):window.getComputedStyle(t)[e]),H(i.type)){const t=$(i,100,i.type);i.ease=q()?t.ease:"easeOut",i.duration=j(t.duration),i.type="keyframes"}else i.ease=i.ease||"easeOut";const r=()=>{this.setValue(t,e,An(n,i)),this.cancel(),this.resolveFinishedPromise()},a=()=>{this.setValue=s?wl:Pl,this.options=i,this.updateFinishedPromise(),this.removeAnimation=()=>{const n=Tl.get(t);n&&n.delete(e)}};Ei()?(super(Ai(t,e,n,i)),a(),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=r,bl(t).set(e,this)):(super(),a(),r())}then(t,e){return this.currentFinishedPromise.then(t,e)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}play(){"finished"===this.state&&this.updateFinishedPromise(),super.play()}cancel(){this.removeAnimation(),super.cancel()}}const El=t=>function(e,n,i){return new N(function(t,e,n,i){const s=ft(t,i),o=s.length,r=[];for(let t=0;t<o;t++){const i=s[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,o));for(const t in e){const n=e[t],s={...z(a,t)};s.duration=s.duration?j(s.duration):s.duration,s.delay=j(s.delay||0),s.allowFlatten=!a.type&&!a.ease,r.push(new Al(i,t,n,s))}}return r}(e,n,i,t))},Ml=El();function Cl(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return st.update(i,!0),()=>ot(i)}const Vl=new WeakMap;let Rl;function Dl({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=Vl.get(t))||void 0===i||i.forEach(i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})})}function kl(t){t.forEach(Dl)}function Ll(t,e){Rl||"undefined"!=typeof ResizeObserver&&(Rl=new ResizeObserver(kl));const n=ft(t);return n.forEach(t=>{let n=Vl.get(t);n||(n=new Set,Vl.set(t,n)),n.add(e),null==Rl||Rl.observe(t)}),()=>{n.forEach(t=>{const n=Vl.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Rl||Rl.unobserve(t)})}}const Bl=new Set;let Fl;function Ol(t){return Bl.add(t),Fl||(Fl=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Bl.forEach(t=>t(e))},window.addEventListener("resize",Fl)),()=>{Bl.delete(t),!Bl.size&&Fl&&(Fl=void 0)}}const jl={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Il(t,e,n,i){const s=n[e],{length:o,position:r}=jl[e],a=s.current,l=n.time;s.current=t["scroll"+r],s.scrollLength=t["scroll"+o]-t["client"+o],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=F(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:U(s.current-a,u)}const Ul={start:0,center:.5,end:1};function Wl(t,e,n=0){let i=0;if(t in Ul&&(t=Ul[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const Nl=[0,0];function zl(t,e,n,i){let s=Array.isArray(t)?t:Nl,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,Ul[t]?t:"0"]),o=Wl(s[0],n,i),r=Wl(s[1],e),o-r}const Xl={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},$l={x:0,y:0};function Hl(t,e,n){const{offset:i=Xl.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):$l,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let t=0;t<h;t++){const n=zl(i[t],u[r],l[r],a[o]);c||n===e[o].interpolatorOffsets[t]||(c=!0),e[o].offset[t]=n}c&&(e[o].interpolate=fi(e[o].offset,yi(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=ie(0,1,e[o].interpolate(e[o].current))}function Yl(t,e,n,i={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){Il(t,"x",e,n),Il(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&Hl(t,n,i)},notify:()=>e(n)}}const Kl=new WeakMap,Gl=new WeakMap,_l=new WeakMap,ql=t=>t===document.documentElement?window:t;function Zl(t,{container:e=document.documentElement,...n}={}){let i=_l.get(e);i||(i=new Set,_l.set(e,i));const s=Yl(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!Kl.has(e)){const t=()=>{for(const t of i)t.measure()},n=()=>{for(const t of i)t.update(rt.timestamp)},s=()=>{for(const t of i)t.notify()},a=()=>{st.read(t,!1,!0),st.read(n,!1,!0),st.update(s,!1,!0)};Kl.set(e,a);const l=ql(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&Gl.set(e,(r=a,"function"==typeof(o=e)?Ol(o):Ll(o,r))),l.addEventListener("scroll",a,{passive:!0})}var o,r;const a=Kl.get(e);return st.read(a,!1,!0),()=>{var t;ot(a);const n=_l.get(e);if(!n)return;if(n.delete(s),n.size)return;const i=Kl.get(e);Kl.delete(e),i&&(ql(e).removeEventListener("scroll",i),null===(t=Gl.get(e))||void 0===t||t(),window.removeEventListener("resize",i))}}const Jl=new Map;function Ql({source:t,container:e=document.documentElement,axis:n="y"}={}){t&&(e=t),Jl.has(e)||Jl.set(e,{});const i=Jl.get(e);return i[n]||(i[n]=W()?new ScrollTimeline({source:e,axis:n}):function({source:t,container:e,axis:n="y"}){t&&(e=t);const i={value:0},s=Zl(t=>{i.value=100*t[n].progress},{container:e,axis:n});return{currentTime:i,cancel:s}}({source:e,axis:n})),i[n]}function tu(t){return t&&(t.target||t.offset)}function eu(t,{axis:e="y",...n}={}){const i={axis:e,...n};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)||tu(e)?Zl(n=>{t(n[e.axis].progress,n)},e):Cl(t,Ql(e))}(t,i):function(t,e){if(t.flatten(),tu(e))return t.pause(),Zl(n=>{t.time=t.duration*n[e.axis].progress},e);{const n=Ql(e);return t.attachTimeline?t.attachTimeline(n,t=>(t.pause(),Cl(e=>{t.time=t.duration*e},n))):R}}(t,i)}const nu={some:0,all:1};function iu(t,e,{root:n,margin:i,amount:s="some"}={}){const o=ft(t),r=new WeakMap,a=new IntersectionObserver(t=>{t.forEach(t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))})},{root:n,rootMargin:i,threshold:"number"==typeof s?s:nu[s]});return o.forEach(t=>a.observe(t)),()=>a.disconnect()}const su=Qo(Fa());function ou(t){return e.useEffect(()=>()=>t(),[])}const ru={renderer:ja,...xr,...na},au={...ru,...Kr,...ia},lu={renderer:ja,...xr};function uu(t,n,i){e.useInsertionEffect(()=>t.on(n,i),[t,n,i])}function cu(t,e){D(Boolean(!e||e.current))}const hu=()=>({scrollX:Rt(0),scrollY:Rt(0),scrollXProgress:Rt(0),scrollYProgress:Rt(0)});function du({container:t,target:n,layoutEffect:i=!0,...s}={}){const o=f(hu);return(i?y:e.useEffect)(()=>(cu(0,n),cu(0,t),eu((t,{x:e,y:n})=>{o.scrollX.set(e.current),o.scrollXProgress.set(e.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)},{...s,container:(null==t?void 0:t.current)||void 0,target:(null==n?void 0:n.current)||void 0})),[t,n,JSON.stringify(s.offset)]),o}function pu(t,e){return e?t+e:t}function mu(t){return"number"==typeof t?t:parseFloat(t)}function fu(t){const n=e.useRef(0),{isStatic:i}=e.useContext(x);e.useEffect(()=>{if(i)return;const e=({timestamp:e,delta:i})=>{n.current||(n.current=e),t(e-n.current,i)};return st.update(e,!0),()=>ot(e)},[t])}class gu extends Vt{constructor(){super(...arguments),this.values=[]}add(t){const e=function(t){return ee.has(t)?"transform":bi.has(t)?Fi(t):void 0}(t);e&&(C(this.values,e),this.update())}update(){this.set(this.values.length?this.values.join(", "):"auto")}}function yu(){!yo.current&&vo();const[t]=e.useState(go.current);return t}function vu(t,e){[...e].reverse().forEach(n=>{const i=t.getVariant(n);i&&nr(t,i),t.variantChildren&&t.variantChildren.forEach(t=>{vu(t,e)})})}function xu(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach(t=>{i.push(lr(t,e,{transitionOverride:n}))}),Promise.all(i)},set:e=>t.forEach(t=>{!function(t,e){Array.isArray(e)?vu(t,e):"string"==typeof e?vu(t,[e]):nr(t,e)}(t,e)}),stop(){t.forEach(t=>{!function(t){t.values.forEach(t=>t.stop())}(t)})},mount:()=>()=>{e.stop()}};return e}function wu(){const t=f(xu);return y(t.mount,[]),t}const Pu=wu;class Su{constructor(){this.componentControls=new Set}subscribe(t){return this.componentControls.add(t),()=>this.componentControls.delete(t)}start(t,e){this.componentControls.forEach(n=>{n.start(t.nativeEvent||t,e)})}}const Tu=()=>new Su;function bu(t){return null!==t&&"object"==typeof t&&aa in t}function Au(){return Eu}function Eu(t){ro.current&&(ro.current.isUpdating=!1,ro.current.blockUpdate(),t&&t())}const Mu=new Map,Cu=new Map,Vu=(t,e)=>`${t}: ${ee.has(e)?"transform":e}`;function Ru(t,e,n){var i;const s=Vu(t,e),o=Mu.get(s);if(!o)return null;const{animation:r,startTime:a}=o;function l(){var i;null===(i=window.MotionCancelOptimisedAnimation)||void 0===i||i.call(window,t,e,n)}return r.onfinish=l,null===a||(null===(i=window.MotionHandoffIsComplete)||void 0===i?void 0:i.call(window,t))?(l(),null):a}let Du,ku;const Lu=new Set;function Bu(){Lu.forEach(t=>{t.animation.play(),t.animation.startTime=t.startTime}),Lu.clear()}const Fu=()=>({});class Ou extends Ro{constructor(){super(...arguments),this.measureInstanceViewportBox=vs}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return{}}getBaseTargetFromProps(){}readValueFromInstance(t,e,n){return n.initialState[e]||0}sortInstanceNodePosition(){return 0}}const ju=Ea({scrapeMotionValuesFromProps:Fu,createRenderState:Fu});let Iu=0;const Uu=t=>t>.001?1/t:1e5;t.AcceleratedAnimation=Ci,t.AnimatePresence=({children:t,custom:n,initial:i=!0,onExitComplete:s,presenceAffectsLayout:o=!0,mode:r="sync",propagate:a=!1,anchorX:l="left"})=>{const[u,c]=b(a),p=e.useMemo(()=>E(t),[t]),g=a&&!u?[]:p.map(A),v=e.useRef(!0),x=e.useRef(p),w=f(()=>new Map),[P,T]=e.useState(p),[M,C]=e.useState(p);y(()=>{v.current=!1,x.current=p;for(let t=0;t<M.length;t++){const e=A(M[t]);g.includes(e)?w.delete(e):!0!==w.get(e)&&w.set(e,!1)}},[M,g.length,g.join("-")]);const V=[];if(p!==P){let t=[...p];for(let e=0;e<M.length;e++){const n=M[e],i=A(n);g.includes(i)||(t.splice(e,0,n),V.push(n))}return"wait"===r&&V.length&&(t=V),C(E(t)),T(p),null}const{forceRender:R}=e.useContext(m);return d(h,{children:M.map(t=>{const e=A(t),h=!(a&&!u)&&(p===M||g.includes(e));return d(S,{isPresent:h,initial:!(v.current&&!i)&&void 0,custom:n,presenceAffectsLayout:o,mode:r,onExitComplete:h?void 0:()=>{if(!w.has(e))return;w.set(e,!0);let t=!0;w.forEach(e=>{e||(t=!1)}),t&&(null==R||R(),C(x.current),a&&(null==c||c()),s&&s())},anchorX:l,children:t},e)})})},t.AnimateSharedLayout=({children:t})=>(i.useEffect(()=>{},[]),d(Xo,{id:f(()=>"asl-"+Iu++),children:t})),t.DeprecatedLayoutGroupContext=M,t.DragControls=Su,t.FlatTree=Wi,t.LayoutGroup=Xo,t.LayoutGroupContext=m,t.LazyMotion=function({children:t,features:n,strict:i=!1}){const[,s]=e.useState(!Yo(n)),o=e.useRef(void 0);if(!Yo(n)){const{renderer:t,...e}=n;o.current=t,Ho(e)}return e.useEffect(()=>{Yo(n)&&n().then(({renderer:t,...e})=>{Ho(e),o.current=t,s(!0)})},[]),d($o.Provider,{value:{renderer:o.current,strict:i},children:t})},t.MotionConfig=function({children:t,isValidProp:n,...i}){n&&qo(n),(i={...e.useContext(x),...i}).isStatic=f(()=>i.isStatic);const s=e.useMemo(()=>i,[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return d(x.Provider,{value:s,children:t})},t.MotionConfigContext=x,t.MotionContext=sa,t.MotionGlobalConfig=L,t.MotionValue=Vt,t.PresenceContext=v,t.Reorder=Za,t.SwitchLayoutGroupContext=Xr,t.VisualElement=Ro,t.addPointerEvent=Sr,t.addPointerInfo=Pr,t.addScaleCorrector=Cs,t.animate=xl,t.animateMini=Ml,t.animateValue=Ti,t.animateVisualElement=lr,t.animationControls=xu,t.animations=xr,t.anticipate=_t,t.backIn=Kt,t.backInOut=Gt,t.backOut=Yt,t.buildTransform=Fo,t.calcLength=kt,t.cancelFrame=ot,t.circIn=qt,t.circInOut=Jt,t.circOut=Zt,t.clamp=ie,t.color=Te,t.complex=Re,t.createBox=vs,t.createRendererMotionComponent=ca,t.createScopedAnimate=vl,t.cubicBezier=Xt,t.delay=Ni,t.disableInstantTransitions=function(){Nt.current=!1},t.distance=Ar,t.distance2D=Er,t.domAnimation=ru,t.domMax=au,t.domMin=lu,t.easeIn=ui,t.easeInOut=hi,t.easeOut=ci,t.filterProps=Zo,t.findSpring=ni,t.frame=st,t.frameData=rt,t.hover=vt,t.inView=iu,t.inertia=li,t.interpolate=fi,t.invariant=k,t.isBrowser=g,t.isDragActive=mt,t.isMotionComponent=bu,t.isMotionValue=Wt,t.isValidMotionProp=Go,t.keyframes=vi,t.m=su,t.makeUseVisualState=Ea,t.mirrorEasing=$t,t.mix=Nn,t.motion=Ia,t.motionValue=Rt,t.noop=R,t.optimizedAppearDataAttribute=Oi,t.pipe=Bn,t.press=Mt,t.progress=F,t.px=ve,t.resolveMotionValue=Xi,t.reverseEasing=Ht,t.scroll=eu,t.scrollInfo=Zl,t.spring=ai,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:i}={}){return(s,o)=>{const r="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,o),a=Math.abs(r-s);let l=t*a;if(i){const e=o*t;l=mi(i)(l/e)*e}return e+l}},t.startOptimizedAppearAnimation=function(t,e,n,i,s){if(window.MotionIsMounted)return;const o=t.dataset.framerAppearId;if(!o)return;window.MotionHandoffAnimation=Ru;const r=Vu(o,e);ku||(ku=Ai(t,e,[n[0],n[0]],{duration:1e4,ease:"linear"}),Mu.set(r,{animation:ku,startTime:null}),window.MotionHandoffAnimation=Ru,window.MotionHasOptimisedAnimation=(t,e)=>{if(!t)return!1;if(!e)return Cu.has(t);const n=Vu(t,e);return Boolean(Mu.get(n))},window.MotionHandoffMarkAsComplete=t=>{Cu.has(t)&&Cu.set(t,!0)},window.MotionHandoffIsComplete=t=>!0===Cu.get(t),window.MotionCancelOptimisedAnimation=(t,e,n,i)=>{const s=Vu(t,e),o=Mu.get(s);o&&(n&&void 0===i?n.postRender(()=>{n.postRender(()=>{o.animation.cancel()})}):o.animation.cancel(),n&&i?(Lu.add(o),n.render(Bu)):(Mu.delete(s),Mu.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(t,e,n)=>{var i,s;const o=ji(t);if(!o)return;const r=null===(i=window.MotionHasOptimisedAnimation)||void 0===i?void 0:i.call(window,o,e),a=null===(s=t.props.values)||void 0===s?void 0:s[e];if(!r||!a)return;const l=n.on("change",t=>{var n;a.get()!==t&&(null===(n=window.MotionCancelOptimisedAnimation)||void 0===n||n.call(window,o,e),l())});return l});const a=()=>{ku.cancel();const o=Ai(t,e,n,i);void 0===Du&&(Du=performance.now()),o.startTime=Du,Mu.set(r,{animation:o,startTime:Du}),s&&s(o)};Cu.set(o,!1),ku.ready?ku.ready.then(a).catch(R):a()},t.steps=function(t,e="end"){return n=>{const i=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,s="end"===e?Math.floor(i):Math.ceil(i);return ie(0,1,s/t)}},t.time=dt,t.transform=$a,t.unwrapMotionComponent=function(t){if(bu(t))return t[aa]},t.useAnimate=function(){const t=f(()=>({current:null,animations:[]})),e=f(()=>vl(t));return ou(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimateMini=function(){const t=f(()=>({current:null,animations:[]})),e=f(()=>El(t));return ou(()=>{t.animations.forEach(t=>t.stop())}),[t,e]},t.useAnimation=Pu,t.useAnimationControls=wu,t.useAnimationFrame=fu,t.useCycle=function(...t){const n=e.useRef(0),[i,s]=e.useState(t[n.current]);return[i,e.useCallback(e=>{n.current="number"!=typeof e?Ja(0,t.length,n.current+1):e,s(t[n.current])},[t.length,...t])]},t.useDeprecatedAnimatedState=function(t){const[n,i]=e.useState(t),s=ju({},!1),o=f(()=>new Ou({props:{onUpdate:t=>{i({...t})}},visualState:s,presenceContext:null},{initialState:t}));return e.useLayoutEffect(()=>(o.mount({}),()=>o.unmount()),[o]),[n,f(()=>t=>lr(o,t))]},t.useDeprecatedInvertedScale=function(t){let n=Xa(1),i=Xa(1);const{visualElement:s}=e.useContext(sa);return t?(n=t.scaleX||n,i=t.scaleY||i):s&&(n=s.getValue("scaleX",1),i=s.getValue("scaleY",1)),{scaleX:Ya(n,Uu),scaleY:Ya(i,Uu)}},t.useDomEvent=function(t,n,i,s){e.useEffect(()=>{const e=t.current;if(i&&e)return so(e,n,i,s)},[t,n,i,s])},t.useDragControls=function(){return f(Tu)},t.useElementScroll=function(t){return du({container:t})},t.useForceUpdate=No,t.useInView=function(t,{root:n,margin:i,amount:s,once:o=!1,initial:r=!1}={}){const[a,l]=e.useState(r);return e.useEffect(()=>{if(!t.current||o&&a)return;const e={root:n&&n.current||void 0,margin:i,amount:s};return iu(t.current,()=>(l(!0),o?void 0:()=>l(!1)),e)},[n,t,i,o,s]),a},t.useInstantLayoutTransition=Au,t.useInstantTransition=function(){const[t,n]=No(),i=Au(),s=e.useRef(-1);return e.useEffect(()=>{st.postRender(()=>st.postRender(()=>{n===s.current&&(Nt.current=!1)}))},[n]),e=>{i(()=>{Nt.current=!0,t(),e(),s.current=n+1})}},t.useIsPresent=function(){return null===(t=e.useContext(v))||t.isPresent;var t},t.useIsomorphicLayoutEffect=y,t.useMotionTemplate=function(t,...e){const n=t.length;return Ha(e.filter(Wt),(function(){let i="";for(let s=0;s<n;s++){i+=t[s];const n=e[s];n&&(i+=Wt(n)?n.get():n)}return i}))},t.useMotionValue=Xa,t.useMotionValueEvent=uu,t.usePresence=b,t.usePresenceData=function(){const t=e.useContext(v);return t?t.custom:void 0},t.useReducedMotion=yu,t.useReducedMotionConfig=function(){const t=yu(),{reducedMotion:n}=e.useContext(x);return"never"!==n&&("always"===n||t)},t.useResetProjection=function(){return e.useCallback(()=>{const t=ro.current;t&&t.resetTree()},[])},t.useScroll=du,t.useSpring=function(t,n={}){const{isStatic:i}=e.useContext(x),s=e.useRef(null),o=f(()=>Wt(t)?t.get():t),r=f(()=>"string"==typeof o?o.replace(/[\d.-]/g,""):void 0),a=Xa(o),l=e.useRef(o),u=e.useRef(R),c=()=>{h(),s.current=Ti({keyframes:[mu(a.get()),mu(l.current)],velocity:a.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:u.current})},h=()=>{s.current&&s.current.stop()};return e.useInsertionEffect(()=>a.attach((t,e)=>i?e(t):(l.current=t,u.current=t=>e(pu(t,r)),st.postRender(c),a.get()),h),[JSON.stringify(n)]),y(()=>{if(Wt(t))return t.on("change",t=>a.set(pu(t,r)))},[a,r]),a},t.useTime=function(){const t=Xa(0);return fu(e=>t.set(e)),t},t.useTransform=Ya,t.useUnmountEffect=ou,t.useVelocity=function(t){const e=Xa(t.getVelocity()),n=()=>{const i=t.getVelocity();e.set(i),i&&st.update(n)};return uu(t,"change",()=>{st.update(n,!1,!0)}),e},t.useViewportScroll=function(){return du()},t.useWillChange=function(){return f(()=>new gu("auto"))},t.visualElementStore=wo,t.wrap=Ja}));

{"mappings": ";;;;;;;;;;;;;;;;;A;;;;;;;;;;;;;;;;ACmBA;;oGAEA,CAEA,MAAMkC,iCAAW,GAAG,QAApB,AAAA;AAGA,MAAM,CAACC,yCAAD,EAAsBnC,yCAAtB,CAAA,GAA2CsB,yBAAkB,CAACY,iCAAD,CAAnE,AAAA;AAcA,MAAM,CAACE,oCAAD,EAAiBC,sCAAjB,CAAA,GAAqCF,yCAAmB,CAAqBD,iCAArB,CAA9D,AAAA;AAUA,MAAMjC,yCAA6B,GAAIqC,CAAAA,KAAD,GAAqC;IACzE,MAAM,E,eACJC,aADI,CAAA,E,UAEJC,QAFI,CAAA,EAGJC,IAAI,EAAEC,QAHF,CAAA,E,aAIJC,WAJI,CAAA,E,cAKJC,YALI,CAAA,SAMJC,KAAK,GAAG,IAARA,GANI,GAOFP,KAPJ,AAAM;IAQN,MAAMQ,UAAU,GAAG5B,aAAA,CAAgC,IAAhC,CAAnB,AAAA;IACA,MAAM8B,UAAU,GAAG9B,aAAA,CAAmC,IAAnC,CAAnB,AAAA;IACA,MAAM,CAACuB,IAAI,GAAG,KAAR,EAAeQ,OAAf,CAAA,GAA0BzB,2BAAoB,CAAC;QACnD0B,IAAI,EAAER,QAD6C;QAEnDS,WAAW,EAAER,WAFsC;QAGnDS,QAAQ,EAAER,YAAVQ;KAHkD,CAApD,AAAqD;IAMrD,OAAA,aACE,CAAA,oBAAA,CAAC,oCAAD,EADF;QAEI,KAAK,EAAEb,aADT;QAEE,UAAU,EAAEO,UAFd;QAGE,UAAU,EAAEE,UAHd;QAIE,SAAS,EAAEzB,YAAK,EAJlB;QAKE,OAAO,EAAEA,YAAK,EALhB;QAME,aAAa,EAAEA,YAAK,EANtB;QAOE,IAAI,EAAEkB,IAPR;QAQE,YAAY,EAAEQ,OARhB;QASE,YAAY,EAAE/B,kBAAA,CAAkB,IAAM+B,OAAO,CAAEK,CAAAA,QAAD,GAAc,CAACA,QAAhB;YAAA,CAA/B;QAAA,EAA0D;YAACL,OAAD;SAA1D,CAThB;QAUE,KAAK,EAAEJ,KAAP;KAVF,EAYGL,QAZH,CADF,CACE;CAlBJ,AAiCC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMe,kCAAY,GAAG,eAArB,AAAA;AAMA,MAAMrD,yCAAa,GAAA,aAAGgB,CAAAA,iBAAA,CACpB,CAACoB,KAAD,EAAyCmB,YAAzC,GAA0D;IACxD,MAAM,E,eAAElB,aAAF,CAAA,EAAiB,GAAGmB,YAAH,EAAjB,GAAqCpB,KAA3C,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAACkB,kCAAD,EAAehB,aAAf,CAAhC,AAAA;IACA,MAAMqB,kBAAkB,GAAGxC,sBAAe,CAACqC,YAAD,EAAeE,OAAO,CAACb,UAAvB,CAA1C,AAAA;IACA,OAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,MAAX,EADF,oCAAA,CAAA;QAEI,IAAI,EAAC,QADP;QAEE,eAAA,EAAc,QAFhB;QAGE,eAAA,EAAea,OAAO,CAAClB,IAHzB;QAIE,eAAA,EAAekB,OAAO,CAACE,SAJzB;QAKE,YAAA,EAAYC,8BAAQ,CAACH,OAAO,CAAClB,IAAT,CAApB;KALF,EAMMiB,YANN,EAAA;QAOE,GAAG,EAAEE,kBAPP;QAQE,OAAO,EAAEzC,2BAAoB,CAACmB,KAAK,CAACyB,OAAP,EAAgBJ,OAAO,CAACK,YAAxB,CAA7B;KARF,CAAA,CADF,CACE;CANgB,CAAtB,AAiBG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMC,iCAAW,GAAG,cAApB,AAAA;AAGA,MAAM,CAACC,oCAAD,EAAiBC,sCAAjB,CAAA,GAAqChC,yCAAmB,CAAqB8B,iCAArB,EAAkC;IAC9FG,UAAU,EAAEC,SAAZD;CAD4D,CAA9D,AAAgG;AAchG,MAAMjE,yCAAyC,GAAImC,CAAAA,KAAD,GAA2C;IAC3F,MAAM,E,eAAEC,aAAF,CAAA,E,YAAiB6B,UAAjB,CAAA,E,UAA6B5B,QAA7B,CAAA,E,WAAuC8B,SAAAA,CAAAA,EAAvC,GAAqDhC,KAA3D,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAAC4B,iCAAD,EAAc1B,aAAd,CAAhC,AAAA;IACA,OAAA,aACE,CAAA,oBAAA,CAAC,oCAAD,EADF;QACkB,KAAK,EAAEA,aAAvB;QAAsC,UAAU,EAAE6B,UAAZ;KAAtC,EACGlD,eAAA,CAAesD,GAAf,CAAmBhC,QAAnB,EAA8BiC,CAAAA,KAAD,GAAA,aAC5B,CAAA,oBAAA,CAAC,eAAD,EAFJ;YAEc,OAAO,EAAEL,UAAU,IAAIT,OAAO,CAAClB,IAA/B;SAAV,EAAA,aACE,CAAA,oBAAA,CAAC,aAAD,EADF;YACmB,OAAO,EAAA,IAAxB;YAAyB,SAAS,EAAE6B,SAAX;SAAzB,EACGG,KADH,CADF,CADD;IAAA,CADH,CADF,CAIQ;CAPV,AAcC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,iCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAMC,kCAAY,GAAG,eAArB,AAAA;AAWA,MAAMtE,yCAAa,GAAA,aAAGc,CAAAA,iBAAA,CACpB,CAACoB,KAAD,EAAyCmB,YAAzC,GAA0D;IACxD,MAAMkB,aAAa,GAAGR,sCAAgB,CAACO,kCAAD,EAAepC,KAAK,CAACC,aAArB,CAAtC,AAAA;IACA,MAAM,cAAE6B,UAAU,GAAGO,aAAa,CAACP,UAA7B,GAAyC,GAAGQ,YAAH,EAAzC,GAA6DtC,KAAnE,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAACqC,kCAAD,EAAepC,KAAK,CAACC,aAArB,CAAhC,AAAA;IACA,OAAOoB,OAAO,CAACd,KAAR,GAAA,aACL,CAAA,oBAAA,CAAC,eAAD,EADF;QACY,OAAO,EAAEuB,UAAU,IAAIT,OAAO,CAAClB,IAA/B;KAAV,EAAA,aACE,CAAA,oBAAA,CAAC,uCAAD,EAAA,oCAAA,CAAA,EAAA,EAAuBmC,YAAvB,EADF;QACuC,GAAG,EAAEnB,YAAL;KAArC,CAAA,CADF,CADK,GAIH,IAJJ,CAEI;CAPc,CAAtB,AAUG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAMA,MAAMoB,uCAAiB,GAAA,aAAG3D,CAAAA,iBAAA,CACxB,CAACoB,KAAD,EAA6CmB,YAA7C,GAA8D;IAC5D,MAAM,E,eAAElB,aAAF,CAAA,EAAiB,GAAGqC,YAAH,EAAjB,GAAqCtC,KAA3C,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAACqC,kCAAD,EAAenC,aAAf,CAAhC,AAAA;IACA,OAAA,aAAA,CACE,oFAAA;IACA,gDAAA;IACA,oBAAA,CAAC,mBAAD,EAAA;QAAc,EAAE,EAAEN,WAAlB;QAAwB,cAAc,EAAA,IAAtC;QAAuC,MAAM,EAAE;YAAC0B,OAAO,CAACX,UAAT;SAAR;KAAvC,EAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,GAAX,EADF,oCAAA,CAAA;QAEI,YAAA,EAAYc,8BAAQ,CAACH,OAAO,CAAClB,IAAT,CAApB;KADF,EAEMmC,YAFN,EAAA;QAGE,GAAG,EAAEnB,YAHP,CAIE,4FADA;QAHF;QAKE,KAAK,EAAE;YAAEqB,aAAa,EAAE,MAAjB;YAAyB,GAAGF,YAAY,CAACG,KAAhB;SAAzB;KALT,CAAA,CADF,CACE,EAJJ;CAJsB,CAA1B,AAiBG;AAGH;;oGAEA,CAEA,MAAMC,kCAAY,GAAG,eAArB,AAAA;AAWA,MAAM3E,yCAAa,GAAA,aAAGa,CAAAA,iBAAA,CACpB,CAACoB,KAAD,EAAyCmB,YAAzC,GAA0D;IACxD,MAAMkB,aAAa,GAAGR,sCAAgB,CAACa,kCAAD,EAAe1C,KAAK,CAACC,aAArB,CAAtC,AAAA;IACA,MAAM,cAAE6B,UAAU,GAAGO,aAAa,CAACP,UAA7B,GAAyC,GAAGa,YAAH,EAAzC,GAA6D3C,KAAnE,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAAC2C,kCAAD,EAAe1C,KAAK,CAACC,aAArB,CAAhC,AAAA;IACA,OAAA,aACE,CAAA,oBAAA,CAAC,eAAD,EADF;QACY,OAAO,EAAE6B,UAAU,IAAIT,OAAO,CAAClB,IAA/B;KAAV,EACGkB,OAAO,CAACd,KAAR,GAAA,aACC,CAAA,oBAAA,CAAC,wCAAD,EAAA,oCAAA,CAAA,EAAA,EAAwBoC,YAAxB,EAFJ;QAE0C,GAAG,EAAExB,YAAL;KAAtC,CAAA,CADD,GAAA,aAGC,CAAA,oBAAA,CAAC,2CAAD,EAAA,oCAAA,CAAA,EAAA,EAA2BwB,YAA3B,EAFA;QAEyC,GAAG,EAAExB,YAAL;KAAzC,CAAA,CAJJ,CADF,CAKM;CAVY,CAAtB,AAcG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,kCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAMA,MAAMyB,wCAAkB,GAAA,aAAGhE,CAAAA,iBAAA,CACzB,CAACoB,KAAD,EAA6CmB,YAA7C,GAA8D;IAC5D,MAAME,OAAO,GAAGtB,sCAAgB,CAAC2C,kCAAD,EAAe1C,KAAK,CAACC,aAArB,CAAhC,AAAA;IACA,MAAMS,UAAU,GAAG9B,aAAA,CAA6B,IAA7B,CAAnB,AAAA;IACA,MAAMiE,YAAY,GAAG/D,sBAAe,CAACqC,YAAD,EAAeE,OAAO,CAACX,UAAvB,EAAmCA,UAAnC,CAApC,AAH4D,EAK5D,8FAFA;IAGA9B,gBAAA,CAAgB,IAAM;QACpB,MAAMmE,OAAO,GAAGrC,UAAU,CAACsC,OAA3B,AAAA;QACA,IAAID,OAAJ,EAAa,OAAOrD,iBAAU,CAACqD,OAAD,CAAjB,CAAb;KAFF,EAGG,EAHH,CAGC,CAAA;IAED,OAAA,aACE,CAAA,oBAAA,CAAC,uCAAD,EAAA,oCAAA,CAAA,EAAA,EACM/C,KADN,EADF;QAGI,GAAG,EAAE6C,YAFP,CAGE,wEADA;QAFF;QAKE,SAAS,EAAExB,OAAO,CAAClB,IALrB;QAME,2BAA2B,EAAA,IAN7B;QAOE,gBAAgB,EAAEtB,2BAAoB,CAACmB,KAAK,CAACiD,gBAAP,EAA0BC,CAAAA,KAAD,GAAW;YAAA,IAAA,qBAAA,AAAA;YACxEA,KAAK,CAACC,cAAN,EAAAD,CAAAA;YACA,CAAA,qBAAA,GAAA7B,OAAO,CAACb,UAAR,CAAmBwC,OAAnB,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAA4BI,KAA5B,EAAA,CAAA;SAFoC,CAPxC;QAWE,oBAAoB,EAAEvE,2BAAoB,CAACmB,KAAK,CAACqD,oBAAP,EAA8BH,CAAAA,KAAD,GAAW;YAChF,MAAMI,aAAa,GAAGJ,KAAK,CAACK,MAAN,CAAaD,aAAnC,AAAA;YACA,MAAME,aAAa,GAAGF,aAAa,CAACG,MAAd,KAAyB,CAAzB,IAA8BH,aAAa,CAACI,OAAd,KAA0B,IAA9E,AAAA;YACA,MAAMC,YAAY,GAAGL,aAAa,CAACG,MAAd,KAAyB,CAAzB,IAA8BD,aAAnD,AAHgF,EAKhF,4DAFA;YAGA,0DAAA;YACA,IAAIG,YAAJ,EAAkBT,KAAK,CAACC,cAAN,EAAlB,CAAA;SAPwC,CAX5C,CAoBE,8DADC;QAnBH;QAsBE,cAAc,EAAEtE,2BAAoB,CAACmB,KAAK,CAAC4D,cAAP,EAAwBV,CAAAA,KAAD,GACzDA,KAAK,CAACC,cAAN,EADkC;QAAA,CAApC;KAtBF,CAAA,CADF,CACE;CAbqB,CAA3B,AAwCG;AAGH,oGAAA,CAEA,MAAMU,2CAAqB,GAAA,aAAGjF,CAAAA,iBAAA,CAC5B,CAACoB,KAAD,EAA6CmB,YAA7C,GAA8D;IAC5D,MAAME,OAAO,GAAGtB,sCAAgB,CAAC2C,kCAAD,EAAe1C,KAAK,CAACC,aAArB,CAAhC,AAAA;IACA,MAAM6D,uBAAuB,GAAGlF,aAAA,CAAa,KAAb,CAAhC,AAAA;IACA,MAAMmF,wBAAwB,GAAGnF,aAAA,CAAa,KAAb,CAAjC,AAAA;IAEA,OAAA,aACE,CAAA,oBAAA,CAAC,uCAAD,EAAA,oCAAA,CAAA,EAAA,EACMoB,KADN,EADF;QAGI,GAAG,EAAEmB,YAFP;QAGE,SAAS,EAAE,KAHb;QAIE,2BAA2B,EAAE,KAJ/B;QAKE,gBAAgB,EAAG+B,CAAAA,KAAD,GAAW;YAAA,IAAA,qBAAA,AAAA;YAC3B,CAAA,qBAAA,GAAAlD,KAAK,CAACiD,gBAAN,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAAA,IAAA,CAAAjD,KAAK,EAAoBkD,KAApB,CAAL,CAAA;YAEA,IAAI,CAACA,KAAK,CAACc,gBAAX,EAA6B;gBAAA,IAAA,sBAAA,AAAA;gBAC3B,IAAI,CAACF,uBAAuB,CAACd,OAA7B,EAAsC,AAAA,CAAA,sBAAA,GAAA3B,OAAO,CAACb,UAAR,CAAmBwC,OAAnB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,IAAA,sBAAA,CAA4BI,KAA5B,EAAA,CADX,CAE3B,sFADA;gBAEAF,KAAK,CAACC,cAAN,EAAAD,CAAAA;aACD;YAEDY,uBAAuB,CAACd,OAAxB,GAAkC,KAAlC,CAAAc;YACAC,wBAAwB,CAACf,OAAzB,GAAmC,KAAnC,CAAAe;SAfJ;QAiBE,iBAAiB,EAAGb,CAAAA,KAAD,GAAW;YAAA,IAAA,qBAAA,EAAA,sBAAA,AAAA;YAC5B,CAAA,qBAAA,GAAAlD,KAAK,CAACiE,iBAAN,CAAA,KAAA,IAAA,IAAA,qBAAA,KAAA,KAAA,CAAA,IAAA,qBAAA,CAAA,IAAA,CAAAjE,KAAK,EAAqBkD,KAArB,CAAL,CAAA;YAEA,IAAI,CAACA,KAAK,CAACc,gBAAX,EAA6B;gBAC3BF,uBAAuB,CAACd,OAAxB,GAAkC,IAAlC,CAAAc;gBACA,IAAIZ,KAAK,CAACK,MAAN,CAAaD,aAAb,CAA2BY,IAA3B,KAAoC,aAAxC,EACEH,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC,CAAAe;aANwB,CAU5B,gDAFC;YAGD,mEAAA;YACA,0CAAA;YACA,MAAMI,MAAM,GAAGjB,KAAK,CAACiB,MAArB,AAAA;YACA,MAAMC,eAAe,GAAA,AAAA,CAAA,sBAAA,GAAG/C,OAAO,CAACb,UAAR,CAAmBwC,OAAtB,CAAA,KAAA,IAAA,IAAA,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,sBAAA,CAA4BqB,QAA5B,CAAqCF,MAArC,CAAxB,AAAA;YACA,IAAIC,eAAJ,EAAqBlB,KAAK,CAACC,cAAN,EAAA,CAfO,CAiB5B,iFAFA;YAGA,mFAAA;YACA,qFAAA;YACA,4CAAA;YACA,IAAID,KAAK,CAACK,MAAN,CAAaD,aAAb,CAA2BY,IAA3B,KAAoC,SAApC,IAAiDH,wBAAwB,CAACf,OAA9E,EACEE,KAAK,CAACC,cAAN,EAAAD,CAAAA;SAEH;KAzCH,CAAA,CADF,CACE;CAPwB,CAA9B,AAmDG;AAGH,oGAAA,CA0BA,MAAMoB,uCAAiB,GAAA,aAAG1F,CAAAA,iBAAA,CACxB,CAACoB,KAAD,EAA6CmB,YAA7C,GAA8D;IAC5D,MAAM,E,eAAElB,aAAF,CAAA,E,WAAiBsE,SAAjB,CAAA,E,iBAA4BC,eAA5B,CAAA,E,kBAA6CvB,gBAA7C,CAAA,EAA+D,GAAGN,YAAH,EAA/D,GAAmF3C,KAAzF,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAAC2C,kCAAD,EAAezC,aAAf,CAAhC,AAAA;IACA,MAAMS,UAAU,GAAG9B,aAAA,CAA6B,IAA7B,CAAnB,AAAA;IACA,MAAMiE,YAAY,GAAG/D,sBAAe,CAACqC,YAAD,EAAeT,UAAf,CAApC,AAJ4D,EAM5D,oEAFA;IAGA,wDAAA;IACAlB,qBAAc,EAAdA,CAAAA;IAEA,OAAA,aACE,CAAA,oBAAA,CAAA,eAAA,EAAA,IAAA,EAAA,aACE,CAAA,oBAAA,CAAC,iBAAD,EAFJ;QAGM,OAAO,EAAA,IADT;QAEE,IAAI,EAAA,IAFN;QAGE,OAAO,EAAE+E,SAHX;QAIE,gBAAgB,EAAEC,eAJpB;QAKE,kBAAkB,EAAEvB,gBAApB;KALF,EAAA,aAOE,CAAA,oBAAA,CAAC,uBAAD,EAPF,oCAAA,CAAA;QAQI,IAAI,EAAC,QADP;QAEE,EAAE,EAAE5B,OAAO,CAACE,SAFd;QAGE,kBAAA,EAAkBF,OAAO,CAACoD,aAH5B;QAIE,iBAAA,EAAiBpD,OAAO,CAACqD,OAJ3B;QAKE,YAAA,EAAYlD,8BAAQ,CAACH,OAAO,CAAClB,IAAT,CAApB;KALF,EAMMwC,YANN,EAAA;QAOE,GAAG,EAAEE,YAPP;QAQE,SAAS,EAAE,IAAMxB,OAAO,CAACf,YAAR,CAAqB,KAArB,CAAjB;KARF,CAAA,CAPF,CADF,EAmBGqE,KAAA,CApBL,CAuBQ;CAlCc,CAA1B,AAuCG;AAGH;;oGAEA,CAEA,MAAMG,gCAAU,GAAG,aAAnB,AAAA;AAMA,MAAM9G,yCAAW,GAAA,aAAGY,CAAAA,iBAAA,CAClB,CAACoB,KAAD,EAAuCmB,YAAvC,GAAwD;IACtD,MAAM,E,eAAElB,aAAF,CAAA,EAAiB,GAAG8E,UAAH,EAAjB,GAAmC/E,KAAzC,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAAC+E,gCAAD,EAAa7E,aAAb,CAAhC,AAAA;IACA,OAAA,aAAO,CAAA,oBAAA,CAAC,gBAAD,CAAW,EAAX,EAAP,oCAAA,CAAA;QAAqB,EAAE,EAAEoB,OAAO,CAACqD,OAAZ;KAAd,EAAuCK,UAAvC,EAAA;QAAmD,GAAG,EAAE5D,YAAL;KAAnD,CAAA,CAAP,CAAO;CAJS,CAApB,AAKG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM6D,sCAAgB,GAAG,mBAAzB,AAAA;AAMA,MAAM/G,yCAAiB,GAAA,aAAGW,CAAAA,iBAAA,CACxB,CAACoB,KAAD,EAA6CmB,YAA7C,GAA8D;IAC5D,MAAM,E,eAAElB,aAAF,CAAA,EAAiB,GAAGgF,gBAAH,EAAjB,GAAyCjF,KAA/C,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAACiF,sCAAD,EAAmB/E,aAAnB,CAAhC,AAAA;IACA,OAAA,aAAO,CAAA,oBAAA,CAAC,gBAAD,CAAW,CAAX,EAAP,oCAAA,CAAA;QAAoB,EAAE,EAAEoB,OAAO,CAACoD,aAAZ;KAAb,EAA4CQ,gBAA5C,EAAA;QAA8D,GAAG,EAAE9D,YAAL;KAA9D,CAAA,CAAP,CAAO;CAJe,CAA1B,AAKG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,sCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA,MAAM+D,gCAAU,GAAG,aAAnB,AAAA;AAKA,MAAMhH,yCAAW,GAAA,aAAGU,CAAAA,iBAAA,CAClB,CAACoB,KAAD,EAAuCmB,YAAvC,GAAwD;IACtD,MAAM,E,eAAElB,aAAF,CAAA,EAAiB,GAAGkF,UAAH,EAAjB,GAAmCnF,KAAzC,AAAM;IACN,MAAMqB,OAAO,GAAGtB,sCAAgB,CAACmF,gCAAD,EAAajF,aAAb,CAAhC,AAAA;IACA,OAAA,aACE,CAAA,oBAAA,CAAC,gBAAD,CAAW,MAAX,EADF,oCAAA,CAAA;QAEI,IAAI,EAAC,QAAL;KADF,EAEMkF,UAFN,EAAA;QAGE,GAAG,EAAEhE,YAHP;QAIE,OAAO,EAAEtC,2BAAoB,CAACmB,KAAK,CAACyB,OAAP,EAAgB,IAAMJ,OAAO,CAACf,YAAR,CAAqB,KAArB,CAAtB;QAAA,CAA7B;KAJF,CAAA,CADF,CACE;CALc,CAApB,AAYG;AAGH,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,gCAAA;CAAA,CAAA,CAAA;AAEA,oGAAA,CAEA,SAASkB,8BAAT,CAAkBrB,IAAlB,EAAiC;IAC/B,OAAOA,IAAI,GAAG,MAAH,GAAY,QAAvB,CAAA;CACD;AAED,MAAMiF,wCAAkB,GAAG,oBAA3B,AAAA;AAEA,MAAM,CAACzG,yCAAD,EAAkB0G,uCAAlB,CAAA,GAAuCtG,oBAAa,CAACqG,wCAAD,EAAqB;IAC7EE,WAAW,EAAE5C,kCADgE;IAE7E6C,SAAS,EAAET,gCAFkE;IAG7EU,QAAQ,EAAE,QAAVA;CAHwD,CAA1D,AAA+E;AAQ/E,MAAMC,kCAAyC,GAAG,CAAC,E,SAAEf,OAAAA,CAAAA,EAAH,GAAiB;IACjE,MAAMgB,mBAAmB,GAAGL,uCAAiB,CAACD,wCAAD,CAA7C,AAAA;IAEA,MAAMO,OAAO,GAAI,CAAA,EAAA,EAAID,mBAAmB,CAACJ,WAAY,CAAA,gBAAA,EAAkBI,mBAAmB,CAACH,SAAU,CAArG;;0BAEF,EAA4BG,mBAAmB,CAACH,SAAU,CAA1D;;0EAEA,EAA4EG,mBAAmB,CAACF,QAAS,CAAA,CAJvG,AAIF;IAEE5G,gBAAA,CAAgB,IAAM;QACpB,IAAI8F,OAAJ,EAAa;YACX,MAAMkB,QAAQ,GAAGC,QAAQ,CAACC,cAAT,CAAwBpB,OAAxB,CAAjB,AAAA;YACA,IAAI,CAACkB,QAAL,EAAe,MAAM,IAAIG,KAAJ,CAAUJ,OAAV,CAAN,CAAf;SACD;KAJH,EAKG;QAACA,OAAD;QAAUjB,OAAV;KALH,CAKC,CAAA;IAED,OAAO,IAAP,CAAA;CAhBF,AAiBC;AAED,MAAMsB,8CAAwB,GAAG,0BAAjC,AAAA;AAOA,MAAMC,wCAAqD,GAAG,CAAC,E,YAAEvF,UAAF,CAAA,E,eAAc+D,aAAAA,CAAAA,EAAf,GAAmC;IAC/F,MAAMyB,yBAAyB,GAAGb,uCAAiB,CAACW,8CAAD,CAAnD,AAAA;IACA,MAAML,OAAO,GAAI,CAAA,0EAAA,EAA4EO,yBAAyB,CAACZ,WAAY,CAAA,EAAA,CAAnI,AAAA;IAEA1G,gBAAA,CAAgB,IAAM;QAAA,IAAA,mBAAA,AAAA;QACpB,MAAMuH,aAAa,GAAA,AAAA,CAAA,mBAAA,GAAGzF,UAAU,CAACsC,OAAd,CAAA,KAAA,IAAA,IAAA,mBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAG,mBAAA,CAAoBoD,YAApB,CAAiC,kBAAjC,CAAtB,AADoB,EAEpB,wEADA;QAEA,IAAI3B,aAAa,IAAI0B,aAArB,EAAoC;YAClC,MAAME,cAAc,GAAGR,QAAQ,CAACC,cAAT,CAAwBrB,aAAxB,CAAvB,AAAA;YACA,IAAI,CAAC4B,cAAL,EAAqBC,OAAO,CAACC,IAAR,CAAaZ,OAAb,CAArB,CAAA;SACD;KANH,EAOG;QAACA,OAAD;QAAUjF,UAAV;QAAsB+D,aAAtB;KAPH,CAOC,CAAA;IAED,OAAO,IAAP,CAAA;CAbF,AAcC;AAED,MAAMtG,yCAAI,GAAGR,yCAAb,AAAA;AACA,MAAMS,yCAAO,GAAGR,yCAAhB,AAAA;AACA,MAAMS,yCAAM,GAAGR,yCAAf,AAAA;AACA,MAAMS,yCAAO,GAAGR,yCAAhB,AAAA;AACA,MAAMS,yCAAO,GAAGR,yCAAhB,AAAA;AACA,MAAMS,yCAAK,GAAGR,yCAAd,AAAA;AACA,MAAMS,yCAAW,GAAGR,yCAApB,AAAA;AACA,MAAMS,yCAAK,GAAGR,yCAAd,AAAA;;ADtiBA", "sources": ["packages/react/dialog/src/index.ts", "packages/react/dialog/src/Dialog.tsx"], "sourcesContent": ["export {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n} from './Dialog';\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n} from './Dialog';\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContext, createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { DismissableLayer } from '@radix-ui/react-dismissable-layer';\nimport { FocusScope } from '@radix-ui/react-focus-scope';\nimport { Portal as PortalPrimitive } from '@radix-ui/react-portal';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useFocusGuards } from '@radix-ui/react-focus-guards';\nimport { RemoveScroll } from 'react-remove-scroll';\nimport { hideOthers } from 'aria-hidden';\nimport { Slot } from '@radix-ui/react-slot';\n\nimport type * as Radix from '@radix-ui/react-primitive';\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Dialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst DIALOG_NAME = 'Dialog';\n\ntype ScopedProps<P> = P & { __scopeDialog?: Scope };\nconst [createDialogContext, createDialogScope] = createContextScope(DIALOG_NAME);\n\ntype DialogContextValue = {\n  triggerRef: React.RefObject<HTMLButtonElement>;\n  contentRef: React.RefObject<DialogContentElement>;\n  contentId: string;\n  titleId: string;\n  descriptionId: string;\n  open: boolean;\n  onOpenChange(open: boolean): void;\n  onOpenToggle(): void;\n  modal: boolean;\n};\n\nconst [DialogProvider, useDialogContext] = createDialogContext<DialogContextValue>(DIALOG_NAME);\n\ninterface DialogProps {\n  children?: React.ReactNode;\n  open?: boolean;\n  defaultOpen?: boolean;\n  onOpenChange?(open: boolean): void;\n  modal?: boolean;\n}\n\nconst Dialog: React.FC<DialogProps> = (props: ScopedProps<DialogProps>) => {\n  const {\n    __scopeDialog,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    modal = true,\n  } = props;\n  const triggerRef = React.useRef<HTMLButtonElement>(null);\n  const contentRef = React.useRef<DialogContentElement>(null);\n  const [open = false, setOpen] = useControllableState({\n    prop: openProp,\n    defaultProp: defaultOpen,\n    onChange: onOpenChange,\n  });\n\n  return (\n    <DialogProvider\n      scope={__scopeDialog}\n      triggerRef={triggerRef}\n      contentRef={contentRef}\n      contentId={useId()}\n      titleId={useId()}\n      descriptionId={useId()}\n      open={open}\n      onOpenChange={setOpen}\n      onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      modal={modal}\n    >\n      {children}\n    </DialogProvider>\n  );\n};\n\nDialog.displayName = DIALOG_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'DialogTrigger';\n\ntype DialogTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = Radix.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface DialogTriggerProps extends PrimitiveButtonProps {}\n\nconst DialogTrigger = React.forwardRef<DialogTriggerElement, DialogTriggerProps>(\n  (props: ScopedProps<DialogTriggerProps>, forwardedRef) => {\n    const { __scopeDialog, ...triggerProps } = props;\n    const context = useDialogContext(TRIGGER_NAME, __scopeDialog);\n    const composedTriggerRef = useComposedRefs(forwardedRef, context.triggerRef);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-haspopup=\"dialog\"\n        aria-expanded={context.open}\n        aria-controls={context.contentId}\n        data-state={getState(context.open)}\n        {...triggerProps}\n        ref={composedTriggerRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'DialogPortal';\n\ntype PortalContextValue = { forceMount?: true };\nconst [PortalProvider, usePortalContext] = createDialogContext<PortalContextValue>(PORTAL_NAME, {\n  forceMount: undefined,\n});\n\ntype PortalProps = React.ComponentPropsWithoutRef<typeof PortalPrimitive>;\ninterface DialogPortalProps extends Omit<PortalProps, 'asChild'> {\n  children?: React.ReactNode;\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogPortal: React.FC<DialogPortalProps> = (props: ScopedProps<DialogPortalProps>) => {\n  const { __scopeDialog, forceMount, children, container } = props;\n  const context = useDialogContext(PORTAL_NAME, __scopeDialog);\n  return (\n    <PortalProvider scope={__scopeDialog} forceMount={forceMount}>\n      {React.Children.map(children, (child) => (\n        <Presence present={forceMount || context.open}>\n          <PortalPrimitive asChild container={container}>\n            {child}\n          </PortalPrimitive>\n        </Presence>\n      ))}\n    </PortalProvider>\n  );\n};\n\nDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'DialogOverlay';\n\ntype DialogOverlayElement = DialogOverlayImplElement;\ninterface DialogOverlayProps extends DialogOverlayImplProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogOverlay = React.forwardRef<DialogOverlayElement, DialogOverlayProps>(\n  (props: ScopedProps<DialogOverlayProps>, forwardedRef) => {\n    const portalContext = usePortalContext(OVERLAY_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, props.__scopeDialog);\n    return context.modal ? (\n      <Presence present={forceMount || context.open}>\n        <DialogOverlayImpl {...overlayProps} ref={forwardedRef} />\n      </Presence>\n    ) : null;\n  }\n);\n\nDialogOverlay.displayName = OVERLAY_NAME;\n\ntype DialogOverlayImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DialogOverlayImplProps extends PrimitiveDivProps {}\n\nconst DialogOverlayImpl = React.forwardRef<DialogOverlayImplElement, DialogOverlayImplProps>(\n  (props: ScopedProps<DialogOverlayImplProps>, forwardedRef) => {\n    const { __scopeDialog, ...overlayProps } = props;\n    const context = useDialogContext(OVERLAY_NAME, __scopeDialog);\n    return (\n      // Make sure `Content` is scrollable even when it doesn't live inside `RemoveScroll`\n      // ie. when `Overlay` and `Content` are siblings\n      <RemoveScroll as={Slot} allowPinchZoom shards={[context.contentRef]}>\n        <Primitive.div\n          data-state={getState(context.open)}\n          {...overlayProps}\n          ref={forwardedRef}\n          // We re-enable pointer-events prevented by `Dialog.Content` to allow scrolling the overlay.\n          style={{ pointerEvents: 'auto', ...overlayProps.style }}\n        />\n      </RemoveScroll>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'DialogContent';\n\ntype DialogContentElement = DialogContentTypeElement;\ninterface DialogContentProps extends DialogContentTypeProps {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst DialogContent = React.forwardRef<DialogContentElement, DialogContentProps>(\n  (props: ScopedProps<DialogContentProps>, forwardedRef) => {\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeDialog);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    return (\n      <Presence present={forceMount || context.open}>\n        {context.modal ? (\n          <DialogContentModal {...contentProps} ref={forwardedRef} />\n        ) : (\n          <DialogContentNonModal {...contentProps} ref={forwardedRef} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nDialogContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentTypeElement = DialogContentImplElement;\ninterface DialogContentTypeProps\n  extends Omit<DialogContentImplProps, 'trapFocus' | 'disableOutsidePointerEvents'> {}\n\nconst DialogContentModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, context.contentRef, contentRef);\n\n    // aria-hide everything except the content (better supported equivalent to setting aria-modal)\n    React.useEffect(() => {\n      const content = contentRef.current;\n      if (content) return hideOthers(content);\n    }, []);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={composedRefs}\n        // we make sure focus isn't trapped once `DialogContent` has been closed\n        // (closed !== unmounted when animating out)\n        trapFocus={context.open}\n        disableOutsidePointerEvents\n        onCloseAutoFocus={composeEventHandlers(props.onCloseAutoFocus, (event) => {\n          event.preventDefault();\n          context.triggerRef.current?.focus();\n        })}\n        onPointerDownOutside={composeEventHandlers(props.onPointerDownOutside, (event) => {\n          const originalEvent = event.detail.originalEvent;\n          const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n          const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n\n          // If the event is a right-click, we shouldn't close because\n          // it is effectively as if we right-clicked the `Overlay`.\n          if (isRightClick) event.preventDefault();\n        })}\n        // When focus is trapped, a `focusout` event may still happen.\n        // We make sure we don't trigger our `onDismiss` in such case.\n        onFocusOutside={composeEventHandlers(props.onFocusOutside, (event) =>\n          event.preventDefault()\n        )}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst DialogContentNonModal = React.forwardRef<DialogContentTypeElement, DialogContentTypeProps>(\n  (props: ScopedProps<DialogContentTypeProps>, forwardedRef) => {\n    const context = useDialogContext(CONTENT_NAME, props.__scopeDialog);\n    const hasInteractedOutsideRef = React.useRef(false);\n    const hasPointerDownOutsideRef = React.useRef(false);\n\n    return (\n      <DialogContentImpl\n        {...props}\n        ref={forwardedRef}\n        trapFocus={false}\n        disableOutsidePointerEvents={false}\n        onCloseAutoFocus={(event) => {\n          props.onCloseAutoFocus?.(event);\n\n          if (!event.defaultPrevented) {\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            // Always prevent auto focus because we either focus manually or want user agent focus\n            event.preventDefault();\n          }\n\n          hasInteractedOutsideRef.current = false;\n          hasPointerDownOutsideRef.current = false;\n        }}\n        onInteractOutside={(event) => {\n          props.onInteractOutside?.(event);\n\n          if (!event.defaultPrevented) {\n            hasInteractedOutsideRef.current = true;\n            if (event.detail.originalEvent.type === 'pointerdown') {\n              hasPointerDownOutsideRef.current = true;\n            }\n          }\n\n          // Prevent dismissing when clicking the trigger.\n          // As the trigger is already setup to close, without doing so would\n          // cause it to close and immediately open.\n          const target = event.target as HTMLElement;\n          const targetIsTrigger = context.triggerRef.current?.contains(target);\n          if (targetIsTrigger) event.preventDefault();\n\n          // On Safari if the trigger is inside a container with tabIndex={0}, when clicked\n          // we will get the pointer down outside event on the trigger, but then a subsequent\n          // focus outside event on the container, we ignore any focus outside event when we've\n          // already had a pointer down outside event.\n          if (event.detail.originalEvent.type === 'focusin' && hasPointerDownOutsideRef.current) {\n            event.preventDefault();\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype DialogContentImplElement = React.ElementRef<typeof DismissableLayer>;\ntype DismissableLayerProps = Radix.ComponentPropsWithoutRef<typeof DismissableLayer>;\ntype FocusScopeProps = Radix.ComponentPropsWithoutRef<typeof FocusScope>;\ninterface DialogContentImplProps extends Omit<DismissableLayerProps, 'onDismiss'> {\n  /**\n   * When `true`, focus cannot escape the `Content` via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapFocus?: FocusScopeProps['trapped'];\n\n  /**\n   * Event handler called when auto-focusing on open.\n   * Can be prevented.\n   */\n  onOpenAutoFocus?: FocusScopeProps['onMountAutoFocus'];\n\n  /**\n   * Event handler called when auto-focusing on close.\n   * Can be prevented.\n   */\n  onCloseAutoFocus?: FocusScopeProps['onUnmountAutoFocus'];\n}\n\nconst DialogContentImpl = React.forwardRef<DialogContentImplElement, DialogContentImplProps>(\n  (props: ScopedProps<DialogContentImplProps>, forwardedRef) => {\n    const { __scopeDialog, trapFocus, onOpenAutoFocus, onCloseAutoFocus, ...contentProps } = props;\n    const context = useDialogContext(CONTENT_NAME, __scopeDialog);\n    const contentRef = React.useRef<HTMLDivElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n\n    // Make sure the whole tree has focus guards as our `Dialog` will be\n    // the last element in the DOM (beacuse of the `Portal`)\n    useFocusGuards();\n\n    return (\n      <>\n        <FocusScope\n          asChild\n          loop\n          trapped={trapFocus}\n          onMountAutoFocus={onOpenAutoFocus}\n          onUnmountAutoFocus={onCloseAutoFocus}\n        >\n          <DismissableLayer\n            role=\"dialog\"\n            id={context.contentId}\n            aria-describedby={context.descriptionId}\n            aria-labelledby={context.titleId}\n            data-state={getState(context.open)}\n            {...contentProps}\n            ref={composedRefs}\n            onDismiss={() => context.onOpenChange(false)}\n          />\n        </FocusScope>\n        {process.env.NODE_ENV !== 'production' && (\n          <>\n            <TitleWarning titleId={context.titleId} />\n            <DescriptionWarning contentRef={contentRef} descriptionId={context.descriptionId} />\n          </>\n        )}\n      </>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * DialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'DialogTitle';\n\ntype DialogTitleElement = React.ElementRef<typeof Primitive.h2>;\ntype PrimitiveHeading2Props = Radix.ComponentPropsWithoutRef<typeof Primitive.h2>;\ninterface DialogTitleProps extends PrimitiveHeading2Props {}\n\nconst DialogTitle = React.forwardRef<DialogTitleElement, DialogTitleProps>(\n  (props: ScopedProps<DialogTitleProps>, forwardedRef) => {\n    const { __scopeDialog, ...titleProps } = props;\n    const context = useDialogContext(TITLE_NAME, __scopeDialog);\n    return <Primitive.h2 id={context.titleId} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'DialogDescription';\n\ntype DialogDescriptionElement = React.ElementRef<typeof Primitive.p>;\ntype PrimitiveParagraphProps = Radix.ComponentPropsWithoutRef<typeof Primitive.p>;\ninterface DialogDescriptionProps extends PrimitiveParagraphProps {}\n\nconst DialogDescription = React.forwardRef<DialogDescriptionElement, DialogDescriptionProps>(\n  (props: ScopedProps<DialogDescriptionProps>, forwardedRef) => {\n    const { __scopeDialog, ...descriptionProps } = props;\n    const context = useDialogContext(DESCRIPTION_NAME, __scopeDialog);\n    return <Primitive.p id={context.descriptionId} {...descriptionProps} ref={forwardedRef} />;\n  }\n);\n\nDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DialogClose\n * -----------------------------------------------------------------------------------------------*/\n\nconst CLOSE_NAME = 'DialogClose';\n\ntype DialogCloseElement = React.ElementRef<typeof Primitive.button>;\ninterface DialogCloseProps extends PrimitiveButtonProps {}\n\nconst DialogClose = React.forwardRef<DialogCloseElement, DialogCloseProps>(\n  (props: ScopedProps<DialogCloseProps>, forwardedRef) => {\n    const { __scopeDialog, ...closeProps } = props;\n    const context = useDialogContext(CLOSE_NAME, __scopeDialog);\n    return (\n      <Primitive.button\n        type=\"button\"\n        {...closeProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, () => context.onOpenChange(false))}\n      />\n    );\n  }\n);\n\nDialogClose.displayName = CLOSE_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst TITLE_WARNING_NAME = 'DialogTitleWarning';\n\nconst [WarningProvider, useWarningContext] = createContext(TITLE_WARNING_NAME, {\n  contentName: CONTENT_NAME,\n  titleName: TITLE_NAME,\n  docsSlug: 'dialog',\n});\n\ntype TitleWarningProps = { titleId?: string };\n\nconst TitleWarning: React.FC<TitleWarningProps> = ({ titleId }) => {\n  const titleWarningContext = useWarningContext(TITLE_WARNING_NAME);\n\n  const MESSAGE = `\\`${titleWarningContext.contentName}\\` requires a \\`${titleWarningContext.titleName}\\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \\`${titleWarningContext.titleName}\\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${titleWarningContext.docsSlug}`;\n\n  React.useEffect(() => {\n    if (titleId) {\n      const hasTitle = document.getElementById(titleId);\n      if (!hasTitle) throw new Error(MESSAGE);\n    }\n  }, [MESSAGE, titleId]);\n\n  return null;\n};\n\nconst DESCRIPTION_WARNING_NAME = 'DialogDescriptionWarning';\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<DialogContentElement>;\n  descriptionId?: string;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef, descriptionId }) => {\n  const descriptionWarningContext = useWarningContext(DESCRIPTION_WARNING_NAME);\n  const MESSAGE = `Warning: Missing \\`Description\\` or \\`aria-describedby={undefined}\\` for {${descriptionWarningContext.contentName}}.`;\n\n  React.useEffect(() => {\n    const describedById = contentRef.current?.getAttribute('aria-describedby');\n    // if we have an id and the user hasn't set aria-describedby={undefined}\n    if (descriptionId && describedById) {\n      const hasDescription = document.getElementById(descriptionId);\n      if (!hasDescription) console.warn(MESSAGE);\n    }\n  }, [MESSAGE, contentRef, descriptionId]);\n\n  return null;\n};\n\nconst Root = Dialog;\nconst Trigger = DialogTrigger;\nconst Portal = DialogPortal;\nconst Overlay = DialogOverlay;\nconst Content = DialogContent;\nconst Title = DialogTitle;\nconst Description = DialogDescription;\nconst Close = DialogClose;\n\nexport {\n  createDialogScope,\n  //\n  Dialog,\n  DialogTrigger,\n  DialogPortal,\n  DialogOverlay,\n  DialogContent,\n  DialogTitle,\n  DialogDescription,\n  DialogClose,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Title,\n  Description,\n  Close,\n  //\n  WarningProvider,\n};\nexport type {\n  DialogProps,\n  DialogTriggerProps,\n  DialogPortalProps,\n  DialogOverlayProps,\n  DialogContentProps,\n  DialogTitleProps,\n  DialogDescriptionProps,\n  DialogCloseProps,\n};\n"], "names": ["createDialogScope", "Dialog", "DialogTrigger", "DialogPortal", "DialogOverlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "DialogDescription", "DialogClose", "Root", "<PERSON><PERSON>", "Portal", "Overlay", "Content", "Title", "Description", "Close", "WarningProvider", "React", "composeEventHandlers", "useComposedRefs", "createContext", "createContextScope", "useId", "useControllableState", "Dismissa<PERSON><PERSON><PERSON><PERSON>", "FocusScope", "PortalPrimitive", "Presence", "Primitive", "useFocusGuards", "RemoveScroll", "hideOthers", "Slot", "DIALOG_NAME", "createDialogContext", "Dialog<PERSON><PERSON>", "useDialogContext", "props", "__scopeDialog", "children", "open", "openProp", "defaultOpen", "onOpenChange", "modal", "triggerRef", "useRef", "contentRef", "<PERSON><PERSON><PERSON>", "prop", "defaultProp", "onChange", "useCallback", "prevOpen", "TRIGGER_NAME", "forwardRef", "forwardedRef", "triggerProps", "context", "composedTriggerRef", "contentId", "getState", "onClick", "onOpenToggle", "PORTAL_NAME", "PortalProvider", "usePortalContext", "forceMount", "undefined", "container", "Children", "map", "child", "OVERLAY_NAME", "portalContext", "overlayProps", "DialogOverlayImpl", "pointerEvents", "style", "CONTENT_NAME", "contentProps", "DialogContentModal", "composedRefs", "useEffect", "content", "current", "onCloseAutoFocus", "event", "preventDefault", "focus", "onPointerDownOutside", "originalEvent", "detail", "ctrlLeftClick", "button", "ctrl<PERSON>ey", "isRightClick", "onFocusOutside", "DialogContentNonModal", "hasInteractedOutsideRef", "hasPointerDownOutsideRef", "defaultPrevented", "onInteractOutside", "type", "target", "targetIsTrigger", "contains", "DialogContentImpl", "trapFocus", "onOpenAutoFocus", "descriptionId", "titleId", "process", "env", "NODE_ENV", "TITLE_NAME", "titleProps", "DESCRIPTION_NAME", "descriptionProps", "CLOSE_NAME", "closeProps", "TITLE_WARNING_NAME", "useWarningContext", "contentName", "<PERSON><PERSON><PERSON>", "docs<PERSON>lug", "TitleWarning", "titleWarningContext", "MESSAGE", "hasTitle", "document", "getElementById", "Error", "DESCRIPTION_WARNING_NAME", "DescriptionWarning", "descriptionWarningContext", "describedById", "getAttribute", "hasDescription", "console", "warn"], "version": 3, "file": "index.mjs.map"}
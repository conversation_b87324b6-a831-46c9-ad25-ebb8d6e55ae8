{"name": "@tabler/icons", "version": "3.31.0", "license": "MIT", "author": "codecalm", "description": "A set of free MIT-licensed high-quality SVG icons for you to use in your web projects.", "repository": {"type": "git", "url": "git+https://github.com/tabler/tabler-icons.git"}, "exports": {"./*": ["./icons/*"]}, "sideEffects": false, "files": ["icons.json", "tabler-nodes-filled.json", "tabler-nodes-outline.json", "icons/*", "categories/*", "docs/*"], "homepage": "https://tabler-icons.io", "bugs": {"url": "https://github.com/tabler/tabler-icons/issues"}, "funding": {"type": "github", "url": "https://github.com/sponsors/codecalm"}, "scripts": {"build": "pnpm run clean && pnpm run copy && pnpm run build:icons && pnpm run build:docs", "build:icons": "node build.mjs", "build:docs": "cp -r ../../docs/* ./docs/", "copy": "pnpm run copy:license", "copy:license": "cp ../../LICENSE ./LICENSE", "clean": "rm -rf dist && find . ! -name '.gitkeep' -path '*/categories/*' -exec rm -rf {} + && rm -rf dist && find . ! -name '.gitkeep' -path '*/icons/*' -exec rm -rf {} +"}, "keywords": ["icons", "svg", "png", "iconfont", "react", "front-end", "web"]}
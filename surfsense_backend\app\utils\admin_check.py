from fastapi import Depends, HTTPException, status
from app.db import User
from app.users import current_active_user

async def check_admin_privileges(user: User = Depends(current_active_user)) -> User:
    """
    Dependency that checks if the current user has admin privileges.
    Returns the user if they are an admin, otherwise raises an HTTPException.
    """
    if not user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions. Admin privileges required."
        )
    return user
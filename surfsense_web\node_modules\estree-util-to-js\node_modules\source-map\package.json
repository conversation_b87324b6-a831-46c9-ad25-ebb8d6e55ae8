{"name": "source-map", "description": "Generates and consumes source maps", "version": "0.7.4", "homepage": "https://github.com/mozilla/source-map", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "usrbincc <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> Smith <<EMAIL>>", "<PERSON> <<EMAIL>>", "azu <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@walmartlabs.com>", "<PERSON> <jeff<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "djchie <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "files": ["source-map.js", "source-map.d.ts", "lib/", "dist/source-map.js"], "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint *.js lib/ test/", "prebuild": "npm run lint", "build": "webpack --color", "pretest": "npm run build", "test": "node test/run-tests.js", "precoverage": "npm run build", "coverage": "nyc node test/run-tests.js", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "dev:watch": "watch 'npm run coverage' lib/ test/", "predev": "npm run setup", "dev": "npm-run-all -p --silent dev:*", "clean": "rm -rf coverage .nyc_output", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^1.3.1", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "nyc": "^11.7.1", "watch": "^1.0.2", "webpack": "^4.9.1", "webpack-cli": "^3.1"}, "nyc": {"reporter": "html"}, "typings": "source-map"}
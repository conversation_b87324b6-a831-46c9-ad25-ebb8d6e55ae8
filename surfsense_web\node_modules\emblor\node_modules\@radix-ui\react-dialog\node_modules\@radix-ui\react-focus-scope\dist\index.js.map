{"mappings": ";;;;;;;;;;;;;;;A;;;;;ACOA,MAAMM,wCAAkB,GAAG,6BAA3B,AAAA;AACA,MAAMC,0CAAoB,GAAG,+BAA7B,AAAA;AACA,MAAMC,mCAAa,GAAG;IAAEC,OAAO,EAAE,KAAX;IAAkBC,UAAU,EAAE,IAAZA;CAAxC,AAAsB;AAItB;;oGAEA,CAEA,MAAMC,sCAAgB,GAAG,YAAzB,AAAA;AAgCA,MAAMX,yCAAU,GAAA,aAAGE,CAAAA,uBAAA,CAAqD,CAACW,KAAD,EAAQC,YAAR,GAAyB;IAC/F,MAAM,QACJC,IAAI,GAAG,KADH,YAEJC,OAAO,GAAG,KAFN,GAGJC,gBAAgB,EAAEC,oBAHd,CAAA,EAIJC,kBAAkB,EAAEC,sBAJhB,CAAA,EAKJ,GAAGC,UAAH,EALI,GAMFR,KANJ,AAAM;IAON,MAAM,CAACS,UAAD,EAAYC,YAAZ,CAAA,GAA4BrB,qBAAA,CAAmC,IAAnC,CAAlC,AAAA;IACA,MAAMe,gBAAgB,GAAGZ,gDAAc,CAACa,oBAAD,CAAvC,AAAA;IACA,MAAMC,kBAAkB,GAAGd,gDAAc,CAACe,sBAAD,CAAzC,AAAA;IACA,MAAMK,qBAAqB,GAAGvB,mBAAA,CAAiC,IAAjC,CAA9B,AAAA;IACA,MAAMyB,YAAY,GAAGxB,8CAAe,CAACW,YAAD,EAAgBc,CAAAA,IAAD,GAAUL,YAAY,CAACK,IAAD,CAArC;IAAA,CAApC,AAAA;IAEA,MAAMC,UAAU,GAAG3B,mBAAA,CAAa;QAC9B4B,MAAM,EAAE,KADsB;QAE9BC,KAAK,IAAG;YACN,IAAA,CAAKD,MAAL,GAAc,IAAd,CAAA;SAH4B;QAK9BE,MAAM,IAAG;YACP,IAAA,CAAKF,MAAL,GAAc,KAAd,CAAA;SACD;KAPgB,CAAA,CAQhBG,OARH,AAd+F,EAwB/F,sFAVgC;IAWhC/B,sBAAA,CAAgB,IAAM;QACpB,IAAIc,OAAJ,EAAa;YACX,SAASmB,aAAT,CAAuBC,KAAvB,EAA0C;gBACxC,IAAIP,UAAU,CAACC,MAAX,IAAqB,CAACR,UAA1B,EAAqC,OAArC;gBACA,MAAMe,MAAM,GAAGD,KAAK,CAACC,MAArB,AAAA;gBACA,IAAIf,UAAS,CAACgB,QAAV,CAAmBD,MAAnB,CAAJ,EACEZ,qBAAqB,CAACQ,OAAtB,GAAgCI,MAAhC,CAAAZ;qBAEAc,2BAAK,CAACd,qBAAqB,CAACQ,OAAvB,EAAgC;oBAAEO,MAAM,EAAE,IAARA;iBAAlC,CAAL,CAAqC;aAExC;YAED,SAASC,cAAT,CAAwBL,KAAxB,EAA2C;gBACzC,IAAIP,UAAU,CAACC,MAAX,IAAqB,CAACR,UAA1B,EAAqC,OAArC;gBACA,MAAMoB,aAAa,GAAGN,KAAK,CAACM,aAA5B,AAFyC,EAIzC,sFAFA;gBAGA,EAAA;gBACA,6EAAA;gBACA,yEAAA;gBACA,EAAA;gBACA,gDAAA;gBACA,EAAA;gBACA,4FAAA;gBACA,yFAAA;gBACA,kFAAA;gBACA,IAAIA,aAAa,KAAK,IAAtB,EAA4B,OAda,CAgBzC,oFAFA;gBAGA,yFAAA;gBACA,IAAI,CAACpB,UAAS,CAACgB,QAAV,CAAmBI,aAAnB,CAAL,EACEH,2BAAK,CAACd,qBAAqB,CAACQ,OAAvB,EAAgC;oBAAEO,MAAM,EAAE,IAARA;iBAAlC,CAAL,CAAqC;aA9B9B,CAkCX,0EAFC;YAGD,0EAAA;YACA,mCAAA;YACA,SAASG,eAAT,CAAyBC,SAAzB,EAAsD;gBACpD,MAAMC,cAAc,GAAGC,QAAQ,CAACC,aAAhC,AAAA;gBACA,KAAK,MAAMC,QAAX,IAAuBJ,SAAvB,CAAkC;oBAChC,IAAII,QAAQ,CAACC,YAAT,CAAsBC,MAAtB,GAA+B,CAAnC,EACE;wBAAA,IAAI,CAAC5B,CAAAA,UAAD,KAAA,IAAA,IAACA,UAAD,KAAA,KAAA,CAAA,IAACA,UAAS,CAAEgB,QAAX,CAAoBO,cAApB,CAAD,CAAA,AAAJ,EAA0CN,2BAAK,CAACjB,UAAD,CAAL,CAA1C;qBAAA,AACD;iBACF;aACF;YAEDwB,QAAQ,CAACK,gBAAT,CAA0B,SAA1B,EAAqChB,aAArC,CAAAW,CAAAA;YACAA,QAAQ,CAACK,gBAAT,CAA0B,UAA1B,EAAsCV,cAAtC,CAAAK,CAAAA;YACA,MAAMM,gBAAgB,GAAG,IAAIC,gBAAJ,CAAqBV,eAArB,CAAzB,AAAA;YACA,IAAIrB,UAAJ,EAAe8B,gBAAgB,CAACE,OAAjB,CAAyBhC,UAAzB,EAAoC;gBAAEiC,SAAS,EAAE,IAAb;gBAAmBC,OAAO,EAAE,IAATA;aAAvD,CAAoC,CAAA;YAEnD,OAAO,IAAM;gBACXV,QAAQ,CAACW,mBAAT,CAA6B,SAA7B,EAAwCtB,aAAxC,CAAAW,CAAAA;gBACAA,QAAQ,CAACW,mBAAT,CAA6B,UAA7B,EAAyChB,cAAzC,CAAAK,CAAAA;gBACAM,gBAAgB,CAACM,UAAjB,EAAAN,CAAAA;aAHF,CAIC;SACF;KAzDH,EA0DG;QAACpC,OAAD;QAAUM,UAAV;QAAqBO,UAAU,CAACC,MAAhC;KA1DH,CA0DC,CAAA;IAED5B,sBAAA,CAAgB,IAAM;QACpB,IAAIoB,UAAJ,EAAe;YACbqC,sCAAgB,CAACC,GAAjB,CAAqB/B,UAArB,CAAA8B,CAAAA;YACA,MAAME,wBAAwB,GAAGf,QAAQ,CAACC,aAA1C,AAAA;YACA,MAAMe,mBAAmB,GAAGxC,UAAS,CAACgB,QAAV,CAAmBuB,wBAAnB,CAA5B,AAAA;YAEA,IAAI,CAACC,mBAAL,EAA0B;gBACxB,MAAMC,UAAU,GAAG,IAAIC,WAAJ,CAAgB1D,wCAAhB,EAAoCE,mCAApC,CAAnB,AAAA;gBACAc,UAAS,CAAC6B,gBAAV,CAA2B7C,wCAA3B,EAA+CW,gBAA/C,CAAAK,CAAAA;gBACAA,UAAS,CAAC2C,aAAV,CAAwBF,UAAxB,CAAAzC,CAAAA;gBACA,IAAI,CAACyC,UAAU,CAACG,gBAAhB,EAAkC;oBAChCC,gCAAU,CAACC,iCAAW,CAACC,2CAAqB,CAAC/C,UAAD,CAAtB,CAAZ,EAAgD;wBAAEkB,MAAM,EAAE,IAARA;qBAAlD,CAAV,CAA0D;oBAC1D,IAAIM,QAAQ,CAACC,aAAT,KAA2Bc,wBAA/B,EACEtB,2BAAK,CAACjB,UAAD,CAAL,CAAAiB;iBAEH;aACF;YAED,OAAO,IAAM;gBACXjB,UAAS,CAACmC,mBAAV,CAA8BnD,wCAA9B,EAAkDW,gBAAlD,CAAA,CADW,CAGX,8DAFAK;gBAGA,gEAAA;gBACA,sDAAA;gBACAgD,UAAU,CAAC,IAAM;oBACf,MAAMC,YAAY,GAAG,IAAIP,WAAJ,CAAgBzD,0CAAhB,EAAsCC,mCAAtC,CAArB,AAAA;oBACAc,UAAS,CAAC6B,gBAAV,CAA2B5C,0CAA3B,EAAiDY,kBAAjD,CAAAG,CAAAA;oBACAA,UAAS,CAAC2C,aAAV,CAAwBM,YAAxB,CAAAjD,CAAAA;oBACA,IAAI,CAACiD,YAAY,CAACL,gBAAlB,EACE3B,2BAAK,CAACsB,wBAAD,KAAA,IAAA,IAACA,wBAAD,KAAA,KAAA,CAAA,GAACA,wBAAD,GAA6Bf,QAAQ,CAAC0B,IAAtC,EAA4C;wBAAEhC,MAAM,EAAE,IAARA;qBAA9C,CAAL,CAAiD;oBALpC,CAOf,0DADC;oBAEDlB,UAAS,CAACmC,mBAAV,CAA8BlD,0CAA9B,EAAoDY,kBAApD,CAAAG,CAAAA;oBAEAqC,sCAAgB,CAACc,MAAjB,CAAwB5C,UAAxB,CAAA8B,CAAAA;iBAVQ,EAWP,CAXO,CAAV,CAWC;aAjBH,CAkBC;SACF;KArCH,EAsCG;QAACrC,UAAD;QAAYL,gBAAZ;QAA8BE,kBAA9B;QAAkDU,UAAlD;KAtCH,CAAA,CArF+F,CA6H/F,iEAFC;IAGD,MAAM6C,aAAa,GAAGxE,wBAAA,CACnBkC,CAAAA,KAAD,GAAgC;QAC9B,IAAI,CAACrB,IAAD,IAAS,CAACC,OAAd,EAAuB,OAAvB;QACA,IAAIa,UAAU,CAACC,MAAf,EAAuB,OAAvB;QAEA,MAAM8C,QAAQ,GAAGxC,KAAK,CAACyC,GAAN,KAAc,KAAd,IAAuB,CAACzC,KAAK,CAAC0C,MAA9B,IAAwC,CAAC1C,KAAK,CAAC2C,OAA/C,IAA0D,CAAC3C,KAAK,CAAC4C,OAAlF,AAAA;QACA,MAAMnC,cAAc,GAAGC,QAAQ,CAACC,aAAhC,AAAA;QAEA,IAAI6B,QAAQ,IAAI/B,cAAhB,EAAgC;YAC9B,MAAMvB,SAAS,GAAGc,KAAK,CAAC6C,aAAxB,AAAA;YACA,MAAM,CAACC,KAAD,EAAQC,IAAR,CAAA,GAAgBC,sCAAgB,CAAC9D,SAAD,CAAtC,AAAA;YACA,MAAM+D,yBAAyB,GAAGH,KAAK,IAAIC,IAA3C,AAH8B,EAK9B,mDAFA;YAGA,IAAI,CAACE,yBAAL,EACE;gBAAA,IAAIxC,cAAc,KAAKvB,SAAvB,EAAkCc,KAAK,CAACkD,cAAN,EAAlC,CAAA;aAAA,MACK;gBACL,IAAI,CAAClD,KAAK,CAACmD,QAAP,IAAmB1C,cAAc,KAAKsC,IAA1C,EAAgD;oBAC9C/C,KAAK,CAACkD,cAAN,EAAAlD,CAAAA;oBACA,IAAIrB,IAAJ,EAAUwB,2BAAK,CAAC2C,KAAD,EAAQ;wBAAE1C,MAAM,EAAE,IAARA;qBAAV,CAAL,CAAa;iBAFzB,MAGO,IAAIJ,KAAK,CAACmD,QAAN,IAAkB1C,cAAc,KAAKqC,KAAzC,EAAgD;oBACrD9C,KAAK,CAACkD,cAAN,EAAAlD,CAAAA;oBACA,IAAIrB,IAAJ,EAAUwB,2BAAK,CAAC4C,IAAD,EAAO;wBAAE3C,MAAM,EAAE,IAARA;qBAAT,CAAL,CAAY;iBACvB;aACF;SACF;KAzBiB,EA2BpB;QAACzB,IAAD;QAAOC,OAAP;QAAgBa,UAAU,CAACC,MAA3B;KA3BoB,CAAtB,AA0BG;IAIH,OAAA,aACE,CAAA,0BAAA,CAAC,sCAAD,CAAW,GAAX,EADF,2DAAA,CAAA;QACiB,QAAQ,EAAE,EAAV;KAAf,EAAiCT,UAAjC,EAAA;QAA6C,GAAG,EAAEM,YAAlD;QAAgE,SAAS,EAAE+C,aAAX;KAAhE,CAAA,CADF,CACE;CA7Je,CAAnB,AA+JC;AAED,aAAA,CAAA,MAAA,CAAA,MAAA,CAAA,yCAAA,EAAA;IAAA,WAAA,EAAA,sCAAA;CAAA,CAAA,CAAA;AAEA;;oGAEA,CAEA;;;GAGA,CACA,SAASP,gCAAT,CAAoBqB,UAApB,EAA+C,UAAEhD,MAAM,GAAG,KAATA,GAAF,GAAqB,EAApE,EAAwE;IACtE,MAAMqB,wBAAwB,GAAGf,QAAQ,CAACC,aAA1C,AAAA;IACA,KAAK,MAAM0C,SAAX,IAAwBD,UAAxB,CAAoC;QAClCjD,2BAAK,CAACkD,SAAD,EAAY;Y,QAAEjD,MAAAA;SAAd,CAAL,CAAiB;QACjB,IAAIM,QAAQ,CAACC,aAAT,KAA2Bc,wBAA/B,EAAyD,OAAzD;KACD;CACF;AAED;;GAEA,CACA,SAASuB,sCAAT,CAA0B9D,SAA1B,EAAkD;IAChD,MAAMkE,UAAU,GAAGnB,2CAAqB,CAAC/C,SAAD,CAAxC,AAAA;IACA,MAAM4D,KAAK,GAAGQ,iCAAW,CAACF,UAAD,EAAalE,SAAb,CAAzB,AAAA;IACA,MAAM6D,IAAI,GAAGO,iCAAW,CAACF,UAAU,CAACG,OAAX,EAAD,EAAuBrE,SAAvB,CAAxB,AAAA;IACA,OAAO;QAAC4D,KAAD;QAAQC,IAAR;KAAP,CAAA;CACD;AAED;;;;;;;;;GASA,CACA,SAASd,2CAAT,CAA+B/C,SAA/B,EAAuD;IACrD,MAAMsE,KAAoB,GAAG,EAA7B,AAAA;IACA,MAAMC,MAAM,GAAG/C,QAAQ,CAACgD,gBAAT,CAA0BxE,SAA1B,EAAqCyE,UAAU,CAACC,YAAhD,EAA8D;QAC3EC,UAAU,EAAGrE,CAAAA,IAAD,GAAe;YACzB,MAAMsE,aAAa,GAAGtE,IAAI,CAACuE,OAAL,KAAiB,OAAjB,IAA4BvE,IAAI,CAACwE,IAAL,KAAc,QAAhE,AAAA;YACA,IAAIxE,IAAI,CAACyE,QAAL,IAAiBzE,IAAI,CAAC0E,MAAtB,IAAgCJ,aAApC,EAAmD,OAAOH,UAAU,CAACQ,WAAlB,CAF1B,CAGzB,2EADA;YAEA,yEAAA;YACA,mDAAA;YACA,OAAO3E,IAAI,CAAC4E,QAAL,IAAiB,CAAjB,GAAqBT,UAAU,CAACU,aAAhC,GAAgDV,UAAU,CAACQ,WAAlE,CAAA;SACD;KARY,CAAf,AAA6E;IAU7E,MAAOV,MAAM,CAACa,QAAP,EAAP,CAA0Bd,KAAK,CAACe,IAAN,CAAWd,MAAM,CAACe,WAAlB,CAAA,CAZ2B,CAarD,gFADA;IAEA,uEAAA;IACA,OAAOhB,KAAP,CAAA;CACD;AAED;;;GAGA,CACA,SAASF,iCAAT,CAAqBmB,QAArB,EAA8CvF,SAA9C,EAAsE;IACpE,KAAK,MAAMwF,OAAX,IAAsBD,QAAtB,CAAgC;QAC9B,uEAAA;QACA,IAAI,CAACE,8BAAQ,CAACD,OAAD,EAAU;YAAEE,IAAI,EAAE1F,SAAN0F;SAAZ,CAAb,EAA6C,OAAOF,OAAP,CAAtB;KACxB;CACF;AAED,SAASC,8BAAT,CAAkBnF,IAAlB,EAAqC,E,MAAEoF,IAAAA,CAAAA,EAAvC,EAAuE;IACrE,IAAIC,gBAAgB,CAACrF,IAAD,CAAhB,CAAuBsF,UAAvB,KAAsC,QAA1C,EAAoD,OAAO,IAAP,CAApD;IACA,MAAOtF,IAAP,CAAa;QACX,mCAAA;QACA,IAAIoF,IAAI,KAAKG,SAAT,IAAsBvF,IAAI,KAAKoF,IAAnC,EAAyC,OAAO,KAAP,CAAzC;QACA,IAAIC,gBAAgB,CAACrF,IAAD,CAAhB,CAAuBwF,OAAvB,KAAmC,MAAvC,EAA+C,OAAO,IAAP,CAA/C;QACAxF,IAAI,GAAGA,IAAI,CAACyF,aAAZ,CAAAzF;KACD;IACD,OAAO,KAAP,CAAA;CACD;AAED,SAAS0F,uCAAT,CAA2BR,OAA3B,EAA8F;IAC5F,OAAOA,OAAO,YAAYS,gBAAnB,IAAuC,QAAA,IAAYT,OAA1D,CAAA;CACD;AAED,SAASvE,2BAAT,CAAeuE,OAAf,EAAiD,UAAEtE,MAAM,GAAG,KAATA,GAAF,GAAqB,EAAtE,EAA0E;IACxE,0CAAA;IACA,IAAIsE,OAAO,IAAIA,OAAO,CAACvE,KAAvB,EAA8B;QAC5B,MAAMsB,wBAAwB,GAAGf,QAAQ,CAACC,aAA1C,AAD4B,EAE5B,iFADA;QAEA+D,OAAO,CAACvE,KAAR,CAAc;YAAEiF,aAAa,EAAE,IAAfA;SAAhB,CAAA,CAH4B,CAI5B,uFADc;QAEd,IAAIV,OAAO,KAAKjD,wBAAZ,IAAwCyD,uCAAiB,CAACR,OAAD,CAAzD,IAAsEtE,MAA1E,EACEsE,OAAO,CAACtE,MAAR,EADF,CAAA;KAED;CACF;AAED;;oGAEA,CAGA,MAAMmB,sCAAgB,GAAG8D,4CAAsB,EAA/C,AAAA;AAEA,SAASA,4CAAT,GAAkC;IAChC,8DAAA,CACA,IAAIC,KAAsB,GAAG,EAA7B,AAAA;IAEA,OAAO;QACL9D,GAAG,EAAC/B,UAAD,EAA4B;YAC7B,mEAAA;YACA,MAAM8F,gBAAgB,GAAGD,KAAK,CAAC,CAAD,CAA9B,AAAA;YACA,IAAI7F,UAAU,KAAK8F,gBAAnB,EACEA,gBAAgB,KAAA,IAAhB,IAAAA,gBAAgB,KAAA,KAAA,CAAhB,IAAAA,gBAAgB,CAAE5F,KAAlB,EAAA4F,CAAAA;YAJ2B,CAM7B,qFADC;YAEDD,KAAK,GAAGE,iCAAW,CAACF,KAAD,EAAQ7F,UAAR,CAAnB,CAAA6F;YACAA,KAAK,CAACG,OAAN,CAAchG,UAAd,CAAA6F,CAAAA;SATG;QAYLjD,MAAM,EAAC5C,UAAD,EAA4B;YAAA,IAAA,OAAA,AAAA;YAChC6F,KAAK,GAAGE,iCAAW,CAACF,KAAD,EAAQ7F,UAAR,CAAnB,CAAA6F;YACA,CAAA,OAAA,GAAAA,KAAK,CAAC,CAAD,CAAL,CAAA,KAAA,IAAA,IAAA,OAAA,KAAA,KAAA,CAAA,IAAA,OAAA,CAAU1F,MAAV,EAAA,CAAA;SACD;KAfH,CAAO;CAiBR;AAED,SAAS4F,iCAAT,CAAwBE,KAAxB,EAAoCC,IAApC,EAA6C;IAC3C,MAAMC,YAAY,GAAG;WAAIF,KAAJ;KAArB,AAAA;IACA,MAAMG,KAAK,GAAGD,YAAY,CAACE,OAAb,CAAqBH,IAArB,CAAd,AAAA;IACA,IAAIE,KAAK,KAAK,EAAd,EACED,YAAY,CAACG,MAAb,CAAoBF,KAApB,EAA2B,CAA3B,CAAAD,CAAAA;IAEF,OAAOA,YAAP,CAAA;CACD;AAED,SAAS5D,iCAAT,CAAqBgE,KAArB,EAA2C;IACzC,OAAOA,KAAK,CAACC,MAAN,CAAcN,CAAAA,IAAD,GAAUA,IAAI,CAAC5B,OAAL,KAAiB,GAAxC;IAAA,CAAP,CAAA;CACD;AAED,MAAMlG,yCAAI,GAAGD,yCAAb,AAAA;;AD3VA", "sources": ["packages/react/focus-scope/src/index.ts", "packages/react/focus-scope/src/FocusScope.tsx"], "sourcesContent": ["export {\n  FocusScope,\n  //\n  Root,\n} from './FocusScope';\nexport type { FocusScopeProps } from './FocusScope';\n", "import * as React from 'react';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\nimport type * as Radix from '@radix-ui/react-primitive';\n\nconst AUTOFOCUS_ON_MOUNT = 'focusScope.autoFocusOnMount';\nconst AUTOFOCUS_ON_UNMOUNT = 'focusScope.autoFocusOnUnmount';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\ntype FocusableTarget = HTMLElement | { focus(): void };\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope\n * -----------------------------------------------------------------------------------------------*/\n\nconst FOCUS_SCOPE_NAME = 'FocusScope';\n\ntype FocusScopeElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = Radix.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface FocusScopeProps extends PrimitiveDivProps {\n  /**\n   * When `true`, tabbing from last item will focus first tabbable\n   * and shift+tab from first item will focus last tababble.\n   * @defaultValue false\n   */\n  loop?: boolean;\n\n  /**\n   * When `true`, focus cannot escape the focus scope via keyboard,\n   * pointer, or a programmatic focus.\n   * @defaultValue false\n   */\n  trapped?: boolean;\n\n  /**\n   * Event handler called when auto-focusing on mount.\n   * Can be prevented.\n   */\n  onMountAutoFocus?: (event: Event) => void;\n\n  /**\n   * Event handler called when auto-focusing on unmount.\n   * Can be prevented.\n   */\n  onUnmountAutoFocus?: (event: Event) => void;\n}\n\nconst FocusScope = React.forwardRef<FocusScopeElement, FocusScopeProps>((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = React.useState<HTMLElement | null>(null);\n  const onMountAutoFocus = useCallbackRef(onMountAutoFocusProp);\n  const onUnmountAutoFocus = useCallbackRef(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = React.useRef<HTMLElement | null>(null);\n  const composedRefs = useComposedRefs(forwardedRef, (node) => setContainer(node));\n\n  const focusScope = React.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    },\n  }).current;\n\n  // Takes care of trapping focus if focus is moved outside programmatically for example\n  React.useEffect(() => {\n    if (trapped) {\n      function handleFocusIn(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const target = event.target as HTMLElement | null;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      function handleFocusOut(event: FocusEvent) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget as HTMLElement | null;\n\n        // A `focusout` event with a `null` `relatedTarget` will happen in at least two cases:\n        //\n        // 1. When the user switches app/tabs/windows/the browser itself loses focus.\n        // 2. In Google Chrome, when the focused element is removed from the DOM.\n        //\n        // We let the browser do its thing here because:\n        //\n        // 1. The browser already keeps a memory of what's focused for when the page gets refocused.\n        // 2. In Google Chrome, if we try to focus the deleted focused element (as per below), it\n        //    throws the CPU to 100%, so we avoid doing anything for this reason here too.\n        if (relatedTarget === null) return;\n\n        // If the focus has moved to an actual legitimate element (`relatedTarget !== null`)\n        // that is outside the container, we move focus to the last valid focused element inside.\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }\n\n      // When the focused element gets removed from the DOM, browsers move focus\n      // back to the document.body. In this case, we move focus to the container\n      // to keep focus trapped correctly.\n      function handleMutations(mutations: MutationRecord[]) {\n        const focusedElement = document.activeElement as HTMLElement | null;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) {\n            if (!container?.contains(focusedElement)) focus(container);\n          }\n        }\n      }\n\n      document.addEventListener('focusin', handleFocusIn);\n      document.addEventListener('focusout', handleFocusOut);\n      const mutationObserver = new MutationObserver(handleMutations);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n\n      return () => {\n        document.removeEventListener('focusin', handleFocusIn);\n        document.removeEventListener('focusout', handleFocusOut);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n\n  React.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement as HTMLElement | null;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n\n        // We hit a react bug (fixed in v17) with focusing in unmount.\n        // We need to delay the focus a little to get around it for now.\n        // See: https://github.com/facebook/react/issues/17894\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          // we need to remove the listener after we `dispatchEvent`\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n\n  // Takes care of looping focus (when tabbing whilst at the edges)\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n\n      const isTabKey = event.key === 'Tab' && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement as HTMLElement | null;\n\n      if (isTabKey && focusedElement) {\n        const container = event.currentTarget as HTMLElement;\n        const [first, last] = getTabbableEdges(container);\n        const hasTabbableElementsInside = first && last;\n\n        // we can only wrap focus if we have tabbable edges\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n\n  return (\n    <Primitive.div tabIndex={-1} {...scopeProps} ref={composedRefs} onKeyDown={handleKeyDown} />\n  );\n});\n\nFocusScope.displayName = FOCUS_SCOPE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Attempts focusing the first element in a list of candidates.\n * Stops when focus has actually moved.\n */\nfunction focusFirst(candidates: HTMLElement[], { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\n\n/**\n * Returns the first and last tabbable elements inside a container.\n */\nfunction getTabbableEdges(container: HTMLElement) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last] as const;\n}\n\n/**\n * Returns a list of potential tabbable candidates.\n *\n * NOTE: This is only a close approximation. For example it doesn't take into account cases like when\n * elements are not visible. This cannot be worked out easily by just reading a property, but rather\n * necessitate runtime knowledge (computed styles, etc). We deal with these cases separately.\n *\n * See: https://developer.mozilla.org/en-US/docs/Web/API/TreeWalker\n * Credit: https://github.com/discord/focus-layers/blob/master/src/util/wrapFocus.tsx#L1\n */\nfunction getTabbableCandidates(container: HTMLElement) {\n  const nodes: HTMLElement[] = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node: any) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden';\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      // `.tabIndex` is not the same as the `tabindex` attribute. It works on the\n      // runtime's understanding of tabbability, so this automatically accounts\n      // for any kind of element that could be tabbed to.\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    },\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement);\n  // we do not take into account the order of nodes with positive `tabIndex` as it\n  // hinders accessibility to have tab order different from visual order.\n  return nodes;\n}\n\n/**\n * Returns the first visible element in a list.\n * NOTE: Only checks visibility up to the `container`.\n */\nfunction findVisible(elements: HTMLElement[], container: HTMLElement) {\n  for (const element of elements) {\n    // we stop checking if it's hidden at the `container` level (excluding)\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\n\nfunction isHidden(node: HTMLElement, { upTo }: { upTo?: HTMLElement }) {\n  if (getComputedStyle(node).visibility === 'hidden') return true;\n  while (node) {\n    // we stop at `upTo` (excluding it)\n    if (upTo !== undefined && node === upTo) return false;\n    if (getComputedStyle(node).display === 'none') return true;\n    node = node.parentElement as HTMLElement;\n  }\n  return false;\n}\n\nfunction isSelectableInput(element: any): element is FocusableTarget & { select: () => void } {\n  return element instanceof HTMLInputElement && 'select' in element;\n}\n\nfunction focus(element?: FocusableTarget | null, { select = false } = {}) {\n  // only focus if that element is focusable\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    // NOTE: we prevent scrolling on focus, to minimize jarring transitions for users\n    element.focus({ preventScroll: true });\n    // only select if its not the same element, it supports selection and we need to select\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FocusScope stack\n * -----------------------------------------------------------------------------------------------*/\n\ntype FocusScopeAPI = { paused: boolean; pause(): void; resume(): void };\nconst focusScopesStack = createFocusScopesStack();\n\nfunction createFocusScopesStack() {\n  /** A stack of focus scopes, with the active one at the top */\n  let stack: FocusScopeAPI[] = [];\n\n  return {\n    add(focusScope: FocusScopeAPI) {\n      // pause the currently active focus scope (at the top of the stack)\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      // remove in case it already exists (because we'll re-add it at the top of the stack)\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n\n    remove(focusScope: FocusScopeAPI) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    },\n  };\n}\n\nfunction arrayRemove<T>(array: T[], item: T) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\n\nfunction removeLinks(items: HTMLElement[]) {\n  return items.filter((item) => item.tagName !== 'A');\n}\n\nconst Root = FocusScope;\n\nexport {\n  FocusScope,\n  //\n  Root,\n};\nexport type { FocusScopeProps };\n"], "names": ["FocusScope", "Root", "React", "useComposedRefs", "Primitive", "useCallbackRef", "AUTOFOCUS_ON_MOUNT", "AUTOFOCUS_ON_UNMOUNT", "EVENT_OPTIONS", "bubbles", "cancelable", "FOCUS_SCOPE_NAME", "forwardRef", "props", "forwardedRef", "loop", "trapped", "onMountAutoFocus", "onMountAutoFocusProp", "onUnmountAutoFocus", "onUnmountAutoFocusProp", "scopeProps", "container", "<PERSON><PERSON><PERSON><PERSON>", "useState", "lastFocusedElementRef", "useRef", "composedRefs", "node", "focusScope", "paused", "pause", "resume", "current", "useEffect", "handleFocusIn", "event", "target", "contains", "focus", "select", "handleFocusOut", "relatedTarget", "handleMutations", "mutations", "focusedElement", "document", "activeElement", "mutation", "removedNodes", "length", "addEventListener", "mutationObserver", "MutationObserver", "observe", "childList", "subtree", "removeEventListener", "disconnect", "focusScopesStack", "add", "previouslyFocusedElement", "hasFocusedCandidate", "mountEvent", "CustomEvent", "dispatchEvent", "defaultPrevented", "focusFirst", "removeLinks", "getTabbableCandidates", "setTimeout", "unmountEvent", "body", "remove", "handleKeyDown", "useCallback", "isTabKey", "key", "altKey", "ctrl<PERSON>ey", "metaKey", "currentTarget", "first", "last", "getTabbableEdges", "hasTabbableElementsInside", "preventDefault", "shift<PERSON>ey", "candidates", "candidate", "findVisible", "reverse", "nodes", "walker", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "acceptNode", "isHiddenInput", "tagName", "type", "disabled", "hidden", "FILTER_SKIP", "tabIndex", "FILTER_ACCEPT", "nextNode", "push", "currentNode", "elements", "element", "isHidden", "upTo", "getComputedStyle", "visibility", "undefined", "display", "parentElement", "isSelectableInput", "HTMLInputElement", "preventScroll", "createFocusScopesStack", "stack", "activeFocusScope", "arrayRemove", "unshift", "array", "item", "updatedArray", "index", "indexOf", "splice", "items", "filter"], "version": 3, "file": "index.js.map"}
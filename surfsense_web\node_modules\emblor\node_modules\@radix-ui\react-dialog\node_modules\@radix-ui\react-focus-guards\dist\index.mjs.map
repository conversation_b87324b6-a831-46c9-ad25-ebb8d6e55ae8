{"mappings": ";;ACAA;AAEA,8EAAA,CACA,IAAII,2BAAK,GAAG,CAAZ,AAAA;AAEA,SAASJ,yCAAT,CAAqBK,KAArB,EAAiC;IAC/BH,yCAAc,EAAdA,CAAAA;IACA,OAAOG,KAAK,CAACC,QAAb,CAAA;CACD;AAED;;;GAGA,CACA,SAASJ,yCAAT,GAA0B;IACxBC,gBAAA,CAAgB,IAAM;QAAA,IAAA,YAAA,EAAA,aAAA,AAAA;QACpB,MAAMK,UAAU,GAAGC,QAAQ,CAACC,gBAAT,CAA0B,0BAA1B,CAAnB,AAAA;QACAD,QAAQ,CAACE,IAAT,CAAcC,qBAAd,CAAoC,YAApC,EAAA,AAAA,CAAA,YAAA,GAAkDJ,UAAU,CAAC,CAAD,CAA5D,CAAA,KAAA,IAAA,IAAA,YAAA,KAAA,KAAA,CAAA,GAAA,YAAA,GAAmEK,sCAAgB,EAAnF,CAAAJ,CAAAA;QACAA,QAAQ,CAACE,IAAT,CAAcC,qBAAd,CAAoC,WAApC,EAAA,AAAA,CAAA,aAAA,GAAiDJ,UAAU,CAAC,CAAD,CAA3D,CAAA,KAAA,IAAA,IAAA,aAAA,KAAA,KAAA,CAAA,GAAA,aAAA,GAAkEK,sCAAgB,EAAlF,CAAAJ,CAAAA;QACAL,2BAAK,EAALA,CAAAA;QAEA,OAAO,IAAM;YACX,IAAIA,2BAAK,KAAK,CAAd,EACEK,QAAQ,CAACC,gBAAT,CAA0B,0BAA1B,CAAA,CAAsDI,OAAtD,CAA+DC,CAAAA,IAAD,GAAUA,IAAI,CAACC,MAAL,EAAxE;YAAA,CAAAP,CAAAA;YAEFL,2BAAK,EAALA,CAAAA;SAJF,CAKC;KAXH,EAYG,EAZH,CAYC,CAAA;CACF;AAED,SAASS,sCAAT,GAA4B;IAC1B,MAAMI,OAAO,GAAGR,QAAQ,CAACS,aAAT,CAAuB,MAAvB,CAAhB,AAAA;IACAD,OAAO,CAACE,YAAR,CAAqB,wBAArB,EAA+C,EAA/C,CAAAF,CAAAA;IACAA,OAAO,CAACG,QAAR,GAAmB,CAAnB,CAAAH;IACAA,OAAO,CAACI,KAAR,CAAcC,OAAd,GAAwB,kEAAxB,CAAAL;IACA,OAAOA,OAAP,CAAA;CACD;AAED,MAAMhB,yCAAI,GAAGD,yCAAb,AAAA;;ADtCA", "sources": ["packages/react/focus-guards/src/index.ts", "packages/react/focus-guards/src/FocusGuards.tsx"], "sourcesContent": ["export {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n} from './FocusGuards';\n", "import * as React from 'react';\n\n/** Number of components which have requested interest to have focus guards */\nlet count = 0;\n\nfunction FocusGuards(props: any) {\n  useFocusGuards();\n  return props.children;\n}\n\n/**\n * Injects a pair of focus guards at the edges of the whole DOM tree\n * to ensure `focusin` & `focusout` events can be caught consistently.\n */\nfunction useFocusGuards() {\n  React.useEffect(() => {\n    const edgeGuards = document.querySelectorAll('[data-radix-focus-guard]');\n    document.body.insertAdjacentElement('afterbegin', edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement('beforeend', edgeGuards[1] ?? createFocusGuard());\n    count++;\n\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll('[data-radix-focus-guard]').forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\n\nfunction createFocusGuard() {\n  const element = document.createElement('span');\n  element.setAttribute('data-radix-focus-guard', '');\n  element.tabIndex = 0;\n  element.style.cssText = 'outline: none; opacity: 0; position: fixed; pointer-events: none';\n  return element;\n}\n\nconst Root = FocusGuards;\n\nexport {\n  FocusGuards,\n  //\n  Root,\n  //\n  useFocusGuards,\n};\n"], "names": ["FocusGuards", "Root", "useFocusGuards", "React", "count", "props", "children", "useEffect", "edgeGuards", "document", "querySelectorAll", "body", "insertAdjacentElement", "createFocusGuard", "for<PERSON>ach", "node", "remove", "element", "createElement", "setAttribute", "tabIndex", "style", "cssText"], "version": 3, "file": "index.mjs.map"}
/**
 * Flux v4.0.4
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the BSD-style license found in the
 * LICENSE file in the root directory of this source tree. An additional grant
 * of patent rights can be found in the PATENTS file in the same directory.
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.FluxUtils=e():t.FluxUtils=e()}(this,function(){return function(t){function e(n){if(r[n])return r[n].exports;var i=r[n]={exports:{},id:n,loaded:!1};return t[n].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var r={};return e.m=t,e.c=r,e.p="",e(0)}([function(t,e,r){t.exports.Container=r(1),t.exports.Mixin=r(9),t.exports.ReduceStore=r(10),t.exports.Store=r(11)},function(t,e,r){"use strict";function n(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function i(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e)}function o(t,e){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function s(t){var e,r;for(e=1;e<arguments.length;e++)r=null!=arguments[e]?arguments[e]:{},e%2?u(Object(r),!0).forEach(function(e){c(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))});return t}function c(t,e,r){return e=a(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function a(t){var e=f(t,"string");return"symbol"==typeof e?e:String(e)}function f(t,e){var r,n;if("object"!=typeof t||null===t)return t;if(r=t[Symbol.toPrimitive],void 0!==r){if(n=r.call(t,e||"default"),"object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function l(t,e){var r,o,u,a,f,l;return h(t),r=s(s({},g),e||{}),o=function(e,n,i){var o=r.withProps?n:void 0,u=r.withContext?i:void 0;return t.calculateState(e,o,u)},u=function(e,n){var i=r.withProps?e:void 0,o=r.withContext?n:void 0;return t.getStores(i,o)},a=function(t){function e(e,r){var i,a=t.call(this,e,r)||this;return c(n(a),"_fluxContainerSubscriptions",void 0),a._fluxContainerSubscriptions=new d,a._fluxContainerSubscriptions.setStores(u(e,r)),a._fluxContainerSubscriptions.addListener(function(){a.setState(function(t,e){return o(t,e,r)})}),i=o(void 0,e,r),a.state=s(s({},a.state||{}),i),a}i(e,t);var a=e.prototype;return a.UNSAFE_componentWillReceiveProps=function(e,n){t.prototype.UNSAFE_componentWillReceiveProps&&t.prototype.UNSAFE_componentWillReceiveProps.call(this,e,n),t.prototype.componentWillReceiveProps&&t.prototype.componentWillReceiveProps.call(this,e,n),(r.withProps||r.withContext)&&(this._fluxContainerSubscriptions.setStores(u(e,n)),this.setState(function(t){return o(t,e,n)}))},a.componentWillUnmount=function(){t.prototype.componentWillUnmount&&t.prototype.componentWillUnmount.call(this),this._fluxContainerSubscriptions.reset()},e}(t),f=r.pure?p(a):a,l=t.displayName||t.name,f.displayName="FluxContainer("+l+")",f}function p(t){var e=function(t){function e(){return t.apply(this,arguments)||this}i(e,t);var r=e.prototype;return r.shouldComponentUpdate=function(t,e){return!b(this.props,t)||!b(this.state,e)},e}(t);return e}function h(t){t.getStores?void 0:_(!1),t.calculateState?void 0:_(!1)}function y(t,e,r,o){var u=function(o){function u(){var t,e,r,i;for(e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return t=o.call.apply(o,[this].concat(r))||this,c(n(t),"state",void 0),t}i(u,o),u.getStores=function(t,r){return e(t,r)},u.calculateState=function(t,e,n){return r(t,e,n)};var s=u.prototype;return s.render=function(){return t(this.state)},u}(m),s=t.displayName||t.name||"FunctionalContainer";return u.displayName=s,l(u,o)}var d=r(2),v=r(5),_=r(4),b=r(8),m=v.Component,g={pure:!0,withProps:!1,withContext:!1};t.exports={create:l,createFunctional:y}},function(t,e,r){"use strict";function n(t,e,r){return e=i(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i(t){var e=o(t,"string");return"symbol"==typeof e?e:String(e)}function o(t,e){var r,n;if("object"!=typeof t||null===t)return t;if(r=t[Symbol.toPrimitive],void 0!==r){if(n=r.call(t,e||"default"),"object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t,e){if(t===e)return!0;if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0}var s=r(3),c=function(){function t(){n(this,"_callbacks",void 0),n(this,"_storeGroup",void 0),n(this,"_stores",void 0),n(this,"_tokens",void 0),this._callbacks=[]}var e=t.prototype;return e.setStores=function(t){var e,r,n,i,o=this;this._stores&&u(this._stores,t)||(this._stores=t,this._resetTokens(),this._resetStoreGroup(),e=!1,r=[],n=function(){e=!0},this._tokens=t.map(function(t){return t.addListener(n)}),i=function(){e&&(o._callbacks.forEach(function(t){return t()}),e=!1)},this._storeGroup=new s(t,i))},e.addListener=function(t){this._callbacks.push(t)},e.reset=function(){this._resetTokens(),this._resetStoreGroup(),this._resetCallbacks(),this._resetStores()},e._resetTokens=function(){this._tokens&&(this._tokens.forEach(function(t){return t.remove()}),this._tokens=null)},e._resetStoreGroup=function(){this._storeGroup&&(this._storeGroup.release(),this._storeGroup=null)},e._resetStores=function(){this._stores=null},e._resetCallbacks=function(){this._callbacks=[]},t}();t.exports=c},function(t,e,r){"use strict";function n(t,e,r){return e=i(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i(t){var e=o(t,"string");return"symbol"==typeof e?e:String(e)}function o(t,e){var r,n;if("object"!=typeof t||null===t)return t;if(r=t[Symbol.toPrimitive],void 0!==r){if(n=r.call(t,e||"default"),"object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function u(t){var e;return t&&t.length?void 0:s(!1),e=t[0].getDispatcher()}var s=r(4),c=function(){function t(t,e){var r,i=this;n(this,"_dispatcher",void 0),n(this,"_dispatchToken",void 0),this._dispatcher=u(t),r=t.map(function(t){return t.getDispatchToken()}),this._dispatchToken=this._dispatcher.register(function(t){i._dispatcher.waitFor(r),e()})}var e=t.prototype;return e.release=function(){this._dispatcher.unregister(this._dispatchToken)},t}();t.exports=c},function(t,e,r){"use strict";function n(t,e){var r,n,o,u,s;for(r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];if(i(e),!t)throw void 0===e?u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings."):(s=0,u=new Error(e.replace(/%s/g,function(){return String(n[s++])})),u.name="Invariant Violation"),u.framesToPop=1,u}var i=function(t){};t.exports=n},function(t,e,r){"use strict";t.exports=r(6)},function(t,e,r){/** @license React v17.0.2
	 * react.production.min.js
	 *
	 * Copyright (c) Facebook, Inc. and its affiliates.
	 *
	 * This source code is licensed under the MIT license found in the
	 * LICENSE file in the root directory of this source tree.
	 */
"use strict";function n(t){return null===t||"object"!=typeof t?null:(t=j&&t[j]||t["@@iterator"],"function"==typeof t?t:null)}function i(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,r=1;r<arguments.length;r++)e+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t,e,r){this.props=t,this.context=e,this.refs=x,this.updater=r||O}function u(){}function s(t,e,r){this.props=t,this.context=e,this.refs=x,this.updater=r||O}function c(t,e,r){var n,i,o,u,s={},c=null,a=null;if(null!=e)for(n in void 0!==e.ref&&(a=e.ref),void 0!==e.key&&(c=""+e.key),e)E.call(e,n)&&!C.hasOwnProperty(n)&&(s[n]=e[n]);if(i=arguments.length-2,1===i)s.children=r;else if(1<i){for(o=Array(i),u=0;u<i;u++)o[u]=arguments[u+2];s.children=o}if(t&&t.defaultProps)for(n in i=t.defaultProps)void 0===s[n]&&(s[n]=i[n]);return{$$typeof:$,type:t,key:c,ref:a,props:s,_owner:k.current}}function a(t,e){return{$$typeof:$,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function f(t){return"object"==typeof t&&null!==t&&t.$$typeof===$}function l(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(t){return e[t]})}function p(t,e){return"object"==typeof t&&null!==t&&null!=t.key?l(""+t.key):e.toString(36)}function h(t,e,r,o,u){var s,c,l,y=typeof t;if("undefined"!==y&&"boolean"!==y||(t=null),s=!1,null===t)s=!0;else switch(y){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case $:case N:s=!0}}if(s)return s=t,u=u(s),t=""===o?"."+p(s,0):o,Array.isArray(u)?(r="",null!=t&&(r=t.replace(T,"$&/")+"/"),h(u,e,r,"",function(t){return t})):null!=u&&(f(u)&&(u=a(u,r+(!u.key||s&&s.key===u.key?"":(""+u.key).replace(T,"$&/")+"/")+t)),e.push(u)),1;if(s=0,o=""===o?".":o+":",Array.isArray(t))for(c=0;c<t.length;c++)y=t[c],l=o+p(y,c),s+=h(y,e,r,l,u);else if(l=n(t),"function"==typeof l)for(t=l.call(t),c=0;!(y=t.next()).done;)y=y.value,l=o+p(y,c++),s+=h(y,e,r,l,u);else if("object"===y)throw e=""+t,Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e));return s}function y(t,e,r){if(null==t)return t;var n=[],i=0;return h(t,n,"","",function(t){return e.call(r,t,i++)}),n}function d(t){if(-1===t._status){var e=t._result;e=e(),t._status=0,t._result=e,e.then(function(e){0===t._status&&(e=e.default,t._status=1,t._result=e)},function(e){0===t._status&&(t._status=2,t._result=e)})}if(1===t._status)return t._result;throw t._result}function v(){var t=R.current;if(null===t)throw Error(i(321));return t}var _,b,m,g,S,w,j,O,x,P,k,E,C,T,R,F,A=r(7),$=60103,N=60106;e.Fragment=60107,e.StrictMode=60108,e.Profiler=60114,_=60109,b=60110,m=60112,e.Suspense=60113,g=60115,S=60116,"function"==typeof Symbol&&Symbol.for&&(w=Symbol.for,$=w("react.element"),N=w("react.portal"),e.Fragment=w("react.fragment"),e.StrictMode=w("react.strict_mode"),e.Profiler=w("react.profiler"),_=w("react.provider"),b=w("react.context"),m=w("react.forward_ref"),e.Suspense=w("react.suspense"),g=w("react.memo"),S=w("react.lazy")),j="function"==typeof Symbol&&Symbol.iterator,O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x={},o.prototype.isReactComponent={},o.prototype.setState=function(t,e){if("object"!=typeof t&&"function"!=typeof t&&null!=t)throw Error(i(85));this.updater.enqueueSetState(this,t,e,"setState")},o.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},u.prototype=o.prototype,P=s.prototype=new u,P.constructor=s,A(P,o.prototype),P.isPureReactComponent=!0,k={current:null},E=Object.prototype.hasOwnProperty,C={key:!0,ref:!0,__self:!0,__source:!0},T=/\/+/g,R={current:null},F={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:{transition:0},ReactCurrentOwner:k,IsSomeRendererActing:{current:!1},assign:A},e.Children={map:y,forEach:function(t,e,r){y(t,function(){e.apply(this,arguments)},r)},count:function(t){var e=0;return y(t,function(){e++}),e},toArray:function(t){return y(t,function(t){return t})||[]},only:function(t){if(!f(t))throw Error(i(143));return t}},e.Component=o,e.PureComponent=s,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=F,e.cloneElement=function(t,e,r){var n,o,u,s,c,a,f;if(null===t||void 0===t)throw Error(i(267,t));if(n=A({},t.props),o=t.key,u=t.ref,s=t._owner,null!=e){void 0!==e.ref&&(u=e.ref,s=k.current),void 0!==e.key&&(o=""+e.key),t.type&&t.type.defaultProps&&(c=t.type.defaultProps);for(a in e)E.call(e,a)&&!C.hasOwnProperty(a)&&(n[a]=void 0===e[a]&&void 0!==c?c[a]:e[a])}if(a=arguments.length-2,1===a)n.children=r;else if(1<a){for(c=Array(a),f=0;f<a;f++)c[f]=arguments[f+2];n.children=c}return{$$typeof:$,type:t.type,key:o,ref:u,props:n,_owner:s}},e.createContext=function(t,e){return void 0===e&&(e=null),t={$$typeof:b,_calculateChangedBits:e,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider={$$typeof:_,_context:t},t.Consumer=t},e.createElement=c,e.createFactory=function(t){var e=c.bind(null,t);return e.type=t,e},e.createRef=function(){return{current:null}},e.forwardRef=function(t){return{$$typeof:m,render:t}},e.isValidElement=f,e.lazy=function(t){return{$$typeof:S,_payload:{_status:-1,_result:t},_init:d}},e.memo=function(t,e){return{$$typeof:g,type:t,compare:void 0===e?null:e}},e.useCallback=function(t,e){return v().useCallback(t,e)},e.useContext=function(t,e){return v().useContext(t,e)},e.useDebugValue=function(){},e.useEffect=function(t,e){return v().useEffect(t,e)},e.useImperativeHandle=function(t,e,r){return v().useImperativeHandle(t,e,r)},e.useLayoutEffect=function(t,e){return v().useLayoutEffect(t,e)},e.useMemo=function(t,e){return v().useMemo(t,e)},e.useReducer=function(t,e,r){return v().useReducer(t,e,r)},e.useRef=function(t){return v().useRef(t)},e.useState=function(t){return v().useState(t)},e.version="17.0.2"},function(t,e){/*
	object-assign
	(c) Sindre Sorhus
	@license MIT
	*/
"use strict";function r(t){if(null===t||void 0===t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}function n(){var t,e,r,n,i;try{if(!Object.assign)return!1;if(t=new String("abc"),t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(e={},r=0;r<10;r++)e["_"+String.fromCharCode(r)]=r;return n=Object.getOwnPropertyNames(e).map(function(t){return e[t]}),"**********"===n.join("")&&(i={},"abcdefghijklmnopqrst".split("").forEach(function(t){i[t]=t}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join(""))}catch(t){return!1}}var i=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,u=Object.prototype.propertyIsEnumerable;t.exports=n()?Object.assign:function(t,e){var n,s,c,a,f,l=r(t);for(c=1;c<arguments.length;c++){n=Object(arguments[c]);for(a in n)o.call(n,a)&&(l[a]=n[a]);if(i)for(s=i(n),f=0;f<s.length;f++)u.call(n,s[f])&&(l[s[f]]=n[s[f]])}return l}},function(t,e){"use strict";function r(t,e){return t===e?0!==t||0!==e||1/t===1/e:t!==t&&e!==e}function n(t,e){var n,o,u;if(r(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;if(n=Object.keys(t),o=Object.keys(e),n.length!==o.length)return!1;for(u=0;u<n.length;u++)if(!i.call(e,n[u])||!r(t[n[u]],e[n[u]]))return!1;return!0}var i=Object.prototype.hasOwnProperty;t.exports=n},function(t,e,r){"use strict";function n(t,e){var r,n,o,u,s,c="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!c){if(Array.isArray(t)||(c=i(t))||e&&t&&"number"==typeof t.length)return c&&(t=c),r=0,n=function(){},{s:n,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:n};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return o=!0,u=!1,{s:function(){c=c.call(t)},n:function(){var t=c.next();return o=t.done,t},e:function(t){u=!0,s=t},f:function(){try{o||null==c.return||c.return()}finally{if(u)throw s}}}}function i(t,e){if(t){if("string"==typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(t,e):void 0}}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function u(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{withProps:!1};return t=t.filter(function(t){return!!t}),{getInitialState:function(){return s(this),e.withProps?this.constructor.calculateState(null,this.props):this.constructor.calculateState(null,void 0)},componentWillMount:function(){var r,n=this,i=!1,o=function(){i=!0};this._fluxMixinSubscriptions=t.map(function(t){return t.addListener(o)}),r=function(){i&&n.setState(function(t){return e.withProps?n.constructor.calculateState(t,n.props):n.constructor.calculateState(t,void 0)}),i=!1},this._fluxMixinStoreGroup=new c(t,r)},componentWillUnmount:function(){var t,e,r;this._fluxMixinStoreGroup.release(),t=n(this._fluxMixinSubscriptions);try{for(t.s();!(e=t.n()).done;)r=e.value,r.remove()}catch(e){t.e(e)}finally{t.f()}this._fluxMixinSubscriptions=[]}}}function s(t){t.constructor.calculateState?void 0:a(!1)}var c=r(3),a=r(4);t.exports=u},function(t,e,r){"use strict";function n(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function i(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e)}function o(t,e){return(o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u(t,e,r){return e=s(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t){var e=c(t,"string");return"symbol"==typeof e?e:String(e)}function c(t,e){var r,n;if("object"!=typeof t||null===t)return t;if(r=t[Symbol.toPrimitive],void 0!==r){if(n=r.call(t,e||"default"),"object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var a=r(11),f=r(18),l=r(4),p=function(t){function e(e){var r;return r=t.call(this,e)||this,u(n(r),"_state",void 0),r._state=r.getInitialState(),r}i(e,t);var r=e.prototype;return r.getState=function(){return this._state},r.getInitialState=function(){return f("FluxReduceStore","getInitialState")},r.reduce=function(t,e){return f("FluxReduceStore","reduce")},r.areEqual=function(t,e){return t===e},r.__invokeOnDispatch=function(t){var e,r;this.__changed=!1,e=this._state,r=this.reduce(e,t),void 0===r?l(!1):void 0,this.areEqual(e,r)||(this._state=r,this.__emitChange()),this.__changed&&this.__emitter.emit(this.__changeEvent)},e}(a);t.exports=p},function(t,e,r){"use strict";function n(t,e,r){return e=i(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function i(t){var e=o(t,"string");return"symbol"==typeof e?e:String(e)}function o(t,e){var r,n;if("object"!=typeof t||null===t)return t;if(r=t[Symbol.toPrimitive],void 0!==r){if(n=r.call(t,e||"default"),"object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var u=r(12),s=u.EventEmitter,c=r(4),a=function(){function t(t){var e=this;n(this,"_dispatchToken",void 0),n(this,"__changed",void 0),n(this,"__changeEvent",void 0),n(this,"__className",void 0),n(this,"__dispatcher",void 0),n(this,"__emitter",void 0),this.__className=this.constructor.name,this.__changed=!1,this.__changeEvent="change",this.__dispatcher=t,this.__emitter=new s,this._dispatchToken=t.register(function(t){e.__invokeOnDispatch(t)})}var e=t.prototype;return e.addListener=function(t){return this.__emitter.addListener(this.__changeEvent,t)},e.getDispatcher=function(){return this.__dispatcher},e.getDispatchToken=function(){return this._dispatchToken},e.hasChanged=function(){return this.__dispatcher.isDispatching()?void 0:c(!1),this.__changed},e.__emitChange=function(){this.__dispatcher.isDispatching()?void 0:c(!1),this.__changed=!0},e.__invokeOnDispatch=function(t){this.__changed=!1,this.__onDispatch(t),this.__changed&&this.__emitter.emit(this.__changeEvent)},e.__onDispatch=function(t){c(!1)},t}();t.exports=a},function(t,e,r){var n={EventEmitter:r(13),EmitterSubscription:r(14)};t.exports=n},function(t,e,r){"use strict";var n=r(14),i=r(16),o=r(4),u=r(17),s=function(){function t(){this._subscriber=new i,this._currentSubscription=null}var e=t.prototype;return e.addListener=function(t,e,r){return this._subscriber.addSubscription(t,new n(this._subscriber,e,r))},e.once=function(t,e,r){var n=this;return this.addListener(t,function(){n.removeCurrentListener(),e.apply(r,arguments)})},e.removeAllListeners=function(t){this._subscriber.removeAllSubscriptions(t)},e.removeCurrentListener=function(){this._currentSubscription?void 0:o(!1),this._subscriber.removeSubscription(this._currentSubscription)},e.listeners=function(t){var e=this._subscriber.getSubscriptionsForType(t);return e?e.filter(u.thatReturnsTrue).map(function(t){return t.listener}):[]},e.emit=function(t){var e,r,n,i,o=this._subscriber.getSubscriptionsForType(t);if(o){for(e=Object.keys(o),r=0;r<e.length;r++)n=e[r],i=o[n],i&&(this._currentSubscription=i,this.__emitToSubscription.apply(this,[i].concat(Array.prototype.slice.call(arguments))));this._currentSubscription=null}},e.__emitToSubscription=function(t,e){var r=Array.prototype.slice.call(arguments,2);t.listener.apply(t.context,r)},t}();t.exports=s},function(t,e,r){"use strict";function n(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i=r(15),o=function(t){function e(e,r,n){var i;return i=t.call(this,e)||this,i.listener=r,i.context=n,i}return n(e,t),e}(i);t.exports=o},function(t,e){"use strict";var r=function(){function t(t){this.subscriber=t}var e=t.prototype;return e.remove=function(){this.subscriber&&(this.subscriber.removeSubscription(this),this.subscriber=null)},t}();t.exports=r},function(t,e,r){"use strict";var n=r(4),i=function(){function t(){this._subscriptionsForType={},this._currentSubscription=null}var e=t.prototype;return e.addSubscription=function(t,e){e.subscriber!==this?n(!1):void 0,this._subscriptionsForType[t]||(this._subscriptionsForType[t]=[]);var r=this._subscriptionsForType[t].length;return this._subscriptionsForType[t].push(e),e.eventType=t,e.key=r,e},e.removeAllSubscriptions=function(t){void 0===t?this._subscriptionsForType={}:delete this._subscriptionsForType[t]},e.removeSubscription=function(t){var e=t.eventType,r=t.key,n=this._subscriptionsForType[e];n&&delete n[r]},e.getSubscriptionsForType=function(t){return this._subscriptionsForType[t]},t}();t.exports=i},function(t,e){"use strict";function r(t){return function(){return t}}var n=function(){};n.thatReturns=r,n.thatReturnsFalse=r(!1),n.thatReturnsTrue=r(!0),n.thatReturnsNull=r(null),n.thatReturnsThis=function(){return this},n.thatReturnsArgument=function(t){return t},t.exports=n},function(t,e,r){"use strict";function n(t,e){i(!1)}var i=r(4);t.exports=n}])});